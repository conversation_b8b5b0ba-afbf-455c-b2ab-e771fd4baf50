import {
  clubDetail,
  clubTablesCode,
  clubOutOfBusiness,
  clubClose,
  clubReopen,
} from "@/api/club";
import { deleteTable, tableCode } from "@/api/table";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button.tsx";
import { Sheet, SheetContent } from "@/components/ui/sheet";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table.tsx";
import { Club } from "@/stores/club.ts";
import { useEffect, useState } from "react";
import { statuses, tableStatuses } from "./dict";
import { GenTable } from "./gen-table";
import { toast } from "sonner";
import { fetchClubList } from "@/stores/async_fn";
import { useDo } from "@/hooks";

export const ClubDetail = ({ id, open, onOpenChange }: any) => {
  const dispatch = useDo();
  const [data, setData] = useState<Club | undefined>(undefined);
  const [edit, setEdit] = useState(false);
  useEffect(() => {
    if (open && id) {
      clubDetail(id).then((data) => {
        setData(data);
      });
    } else {
      setData(undefined);
    }
  }, [id, open, edit]);

  const status = statuses.find((status) => status.value === data?.status);

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent className="max-w-[800px] h-full overflow-hidden">
        <div className="overflow-y-auto h-full">
          <div className="border-b flex pb-2 mb-2">
            <div className="flex-1 text-lg font-bold items-center">
              {data?.name}
            </div>
            <div className="space-x-2 flex">
              <Button
                size="sm"
                variant="outline"
                className="h-8"
                onClick={() => {
                  if (edit) {
                    setEdit(false);
                  } else {
                    onOpenChange(false);
                  }
                }}
              >
                关闭
              </Button>
            </div>
          </div>
          <div className="grid grid-cols-4">
            <div className="p-2">联系人</div>
            <div className="col-span-3 p-2">{data?.phone}</div>
            <div className="p-2">当前状态</div>
            <div className="col-span-3 p-2">{status?.label}</div>
            <div className="p-2">地址</div>
            <div className="col-span-3 p-2">{data?.address}</div>
            <div className="p-2">费率</div>
            <div className="col-span-3 p-2">{data?.serviceFeeRatio}</div>
          </div>

          {/* 门店操作按钮 - 停业状态下不显示任何操作 */}
          {data?.status !== 5 && (
            <div className="border-t pt-4 mt-4">
              <div className="p-2 text-sm font-medium text-gray-700 mb-2">
                门店操作
              </div>
              <div className="flex flex-wrap gap-2 px-2">
                {/* 停业按钮 */}
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button variant="destructive" size="sm" className="h-8">
                      停业门店
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>确认停业门店？</AlertDialogTitle>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>取消</AlertDialogCancel>
                      <AlertDialogAction
                        onClick={async () => {
                          try {
                            await clubOutOfBusiness(id);
                            toast.success("停业成功");
                            onOpenChange(false); // 关闭弹出层
                            dispatch(fetchClubList()); // 刷新列表
                          } catch (error) {
                            toast.error("停业失败");
                          }
                        }}
                      >
                        确认停业
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>

                {/* 关闭按钮 */}
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button variant="destructive" size="sm" className="h-8">
                      关闭门店
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>确认关闭门店？</AlertDialogTitle>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>取消</AlertDialogCancel>
                      <AlertDialogAction
                        onClick={async () => {
                          try {
                            await clubClose(id);
                            toast.success("关闭成功");
                            clubDetail(id).then((data) => {
                              setData(data);
                            });
                          } catch (error) {
                            toast.error("关闭失败");
                          }
                        }}
                      >
                        确认关闭
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>

                {/* 重新打开按钮 - 只有关闭状态才能重新打开 */}
                {data?.status === 4 && (
                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button variant="default" size="sm" className="h-8">
                        重新打开门店
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>确认重新打开门店？</AlertDialogTitle>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>取消</AlertDialogCancel>
                        <AlertDialogAction
                          onClick={async () => {
                            try {
                              await clubReopen(id);
                              toast.success("重新打开成功");
                              clubDetail(id).then((data) => {
                                setData(data);
                              });
                            } catch (error) {
                              toast.error("重新打开失败");
                            }
                          }}
                        >
                          确认重新打开
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                )}
              </div>
            </div>
          )}
          <div className="mx-2">
            <div className="flex space-x-2 py-2">
              <GenTable
                id={id}
                onSubmit={() => {
                  clubDetail(id).then((data) => {
                    setData(data);
                  });
                }}
              />
              <Button
                size="sm"
                variant="outline"
                className="h-8"
                onClick={() => {
                  clubTablesCode(id).then((res) => {
                    const { data, headers } = res;
                    const fileName = headers["content-disposition"].replace(
                      /\w+; filename=(.*)/,
                      "$1"
                    );
                    const a = document.createElement("a");
                    a.href = URL.createObjectURL(data);
                    a.download = decodeURI(fileName);
                    a.click();
                  });
                }}
              >
                打包下载二维码
              </Button>
            </div>
            <Table className="border rounded-sm">
              <TableHeader>
                <TableRow>
                  <TableHead className="w-40%">名称</TableHead>
                  <TableHead className="w-40%">状态</TableHead>
                  <TableHead className="w-20% text-center"></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {data?.tables?.map((table: any, index: number) => (
                  <TableRow key={index}>
                    <TableCell>{table.name}</TableCell>
                    <TableCell>
                      {
                        tableStatuses.find(
                          (status) => status.value === table.status
                        )?.label
                      }
                    </TableCell>
                    <TableCell>
                      <div className="flex space-x-1">
                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <Button
                              variant="destructive"
                              size="sm"
                              className="h-7"
                            >
                              删除
                            </Button>
                          </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>确认删除？</AlertDialogTitle>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel>取消</AlertDialogCancel>
                              <AlertDialogAction
                                onClick={async () => {
                                  deleteTable(table.id).then(() => {
                                    toast.success("删除成功");
                                    clubDetail(id).then((data) => {
                                      setData(data);
                                    });
                                  });
                                }}
                              >
                                删除
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>

                        <Button
                          size="sm"
                          type="button"
                          className="h-7"
                          variant="outline"
                          onClick={() => {
                            tableCode(table.id).then((res) => {
                              const { data, headers } = res;
                              console.log(headers);

                              const fileName = headers[
                                "content-disposition"
                              ].replace(/\w+; filename=(.*)/, "$1");
                              const a = document.createElement("a");
                              a.href = URL.createObjectURL(data);
                              a.download = decodeURI(fileName);
                              a.click();
                            });
                          }}
                        >
                          下载球桌二维码
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
};
