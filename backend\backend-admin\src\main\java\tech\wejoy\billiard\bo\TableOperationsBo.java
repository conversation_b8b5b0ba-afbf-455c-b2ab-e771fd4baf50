package tech.wejoy.billiard.bo;

import lombok.Data;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import tech.wejoy.billiard.enums.OrderPayTypeEnum;
import tech.wejoy.billiard.enums.TableStatusEnum;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class TableOperationsBo {

    @Schema(description = "桌台ID")
    private Long id;

    @Schema(description = "桌台名称")
    private String tableName;

    @Schema(description = "桌台状态")
    private TableStatusEnum status;

    @Schema(description = "开始时间")
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    private LocalDateTime endTime;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "用户联系方式")
    private String userPhone;

    @Schema(description = "用户开台次数")
    private Integer userOrderCount = 1;

    private Long orderId;

    private String orderNo;

    @Schema(description = "支付方式")
    private OrderPayTypeEnum payType;

    private BigDecimal payAmount;

}
