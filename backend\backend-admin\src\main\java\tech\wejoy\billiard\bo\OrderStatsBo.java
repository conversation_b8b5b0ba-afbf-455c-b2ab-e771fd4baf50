package tech.wejoy.billiard.bo;

import lombok.Data;
import org.eclipse.microprofile.openapi.annotations.media.Schema;

@Data
public class OrderStatsBo {

    @Schema(description = "总订单数")
    private Integer total = 0;

    @Schema(description = "已完成订单数")
    private Integer finished = 0;

    @Schema(description = "已取消订单数")
    private Integer canceled = 0;

    @Schema(description = "使用中订单数")
    private Integer using = 0;

}
