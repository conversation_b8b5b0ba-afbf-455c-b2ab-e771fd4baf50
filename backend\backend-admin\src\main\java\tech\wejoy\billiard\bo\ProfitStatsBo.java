package tech.wejoy.billiard.bo;

import lombok.Data;
import org.eclipse.microprofile.openapi.annotations.media.Schema;

import java.math.BigDecimal;

@Data
public class ProfitStatsBo {

    @Schema(description = "总营业额")
    private BigDecimal total = BigDecimal.ZERO;

    @Schema(description = "在线收益")
    private BigDecimal online = BigDecimal.ZERO;

    @Schema(description = "美团券")
    private BigDecimal meituan = BigDecimal.ZERO;

    @Schema(description = "抖音券")
    private BigDecimal douyin = BigDecimal.ZERO;

    @Schema(description = "订单收益")
    private BigDecimal order = BigDecimal.ZERO;

    @Schema(description = "单店卡充值")
    private BigDecimal club = BigDecimal.ZERO;

    @Schema(description = "优惠券")
    private BigDecimal coupon = BigDecimal.ZERO;

    @Schema(description = "单店卡赠送")
    private BigDecimal clubGift = BigDecimal.ZERO;

}
