package tech.wejoy.billiard.dto;

import jakarta.ws.rs.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.eclipse.microprofile.openapi.annotations.media.Schema;

@EqualsAndHashCode(callSuper = true)
@Data
public class ClubDatePeriodDto extends DatePeriodParamDto {
    @QueryParam("clubId")
    private Long clubId;
    @QueryParam("tenantId")
    @Schema(hidden = true)
    private Long tenantId;
}
