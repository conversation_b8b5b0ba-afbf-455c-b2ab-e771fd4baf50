{"duration": 3945, "records": [{"duration": 2111, "stepId": "io.quarkus.smallrye.openapi.deployment.SmallRyeOpenApiProcessor#build", "started": "12:24:37.830", "dependents": [532], "id": 529, "thread": "build-40"}, {"duration": 899, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#enhancerDomainObjects", "started": "12:24:38.096", "dependents": [530, 496, 497], "id": 495, "thread": "build-61"}, {"duration": 763, "stepId": "io.quarkus.deployment.steps.ClassTransformingBuildStep#handleClassTransformation", "started": "12:24:39.223", "dependents": [531], "id": 530, "thread": "build-61"}, {"duration": 616, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#pregenProxies", "started": "12:24:39.987", "dependents": [532], "id": 531, "thread": "build-181"}, {"duration": 392, "stepId": "io.quarkus.deployment.steps.ApplicationIndexBuildStep#build", "started": "12:24:36.978", "dependents": [311, 510, 431, 500, 421, 312, 430], "id": 310, "thread": "build-38"}, {"duration": 381, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#setupDeployment", "started": "12:24:39.236", "dependents": [528, 517, 516, 532, 522, 515, 521, 520, 518], "id": 514, "thread": "build-150"}, {"duration": 366, "stepId": "io.quarkus.deployment.console.ConsoleProcessor#setupConsole", "started": "12:24:36.976", "dependents": [314, 386, 390, 305, 387, 309, 307], "id": 304, "thread": "build-19"}, {"duration": 329, "stepId": "io.quarkus.arc.deployment.ArcProcessor#generateResources", "started": "12:24:38.439", "dependents": [528, 530, 482], "id": 481, "thread": "build-66"}, {"duration": 314, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#parsePersistenceXmlDescriptors", "started": "12:24:36.960", "dependents": [394, 294], "id": 293, "thread": "build-37"}, {"duration": 306, "stepId": "io.quarkus.arc.deployment.ArcProcessor#buildCompatibleExtensions", "started": "12:24:36.932", "dependents": [406, 421], "id": 283, "thread": "build-14"}, {"duration": 303, "stepId": "io.quarkus.deployment.steps.ConfigGenerationBuildStep#generateConfigClass", "started": "12:24:36.981", "dependents": [], "id": 295, "thread": "build-34"}, {"duration": 295, "stepId": "io.quarkus.deployment.logging.LoggingResourceProcessor#logConsoleCommand", "started": "12:24:36.931", "dependents": [502], "id": 278, "thread": "build-21"}, {"duration": 284, "stepId": "io.quarkus.devui.deployment.BuildTimeContentProcessor#createBuildTimeConstJsTemplate", "started": "12:24:38.742", "dependents": [504, 503], "id": 499, "thread": "build-226"}, {"duration": 282, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#produceNamedHttpSecurityPolicies", "started": "12:24:36.923", "dependents": [450, 532, 449, 454], "id": 255, "thread": "build-8"}, {"duration": 282, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#createVertxContextHandlers", "started": "12:24:36.992", "dependents": [299, 532, 296], "id": 292, "thread": "build-53"}, {"duration": 274, "stepId": "io.quarkus.virtual.threads.VirtualThreadsProcessor#setup", "started": "12:24:36.981", "dependents": [450, 532, 406, 449, 421, 454], "id": 290, "thread": "build-12"}, {"duration": 271, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#setupEndpoints", "started": "12:24:38.782", "dependents": [528, 530, 532, 514, 509, 501, 505, 513], "id": 500, "thread": "build-63"}, {"duration": 263, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#createVertxThreadFactory", "started": "12:24:36.958", "dependents": [532, 296], "id": 274, "thread": "build-36"}, {"duration": 259, "stepId": "io.quarkus.vertx.deployment.VertxProcessor#currentContextFactory", "started": "12:24:36.962", "dependents": [532, 482], "id": 269, "thread": "build-16"}, {"duration": 257, "stepId": "io.quarkus.netty.deployment.NettyProcessor#eagerlyInitClass", "started": "12:24:36.945", "dependents": [532], "id": 246, "thread": "build-20"}, {"duration": 256, "stepId": "io.quarkus.deployment.steps.MainClassBuildStep#build", "started": "12:24:40.603", "dependents": [], "id": 532, "thread": "build-61"}, {"duration": 250, "stepId": "io.quarkus.swaggerui.deployment.SwaggerUiProcessor#getSwaggerUiFinalDestination", "started": "12:24:37.743", "dependents": [492], "id": 423, "thread": "build-29"}, {"duration": 247, "stepId": "io.quarkus.smallrye.context.deployment.SmallRyeContextPropagationProcessor#buildStatic", "started": "12:24:36.995", "dependents": [532], "id": 287, "thread": "build-59"}, {"duration": 246, "stepId": "io.quarkus.mutiny.reactive.operators.deployment.MutinyReactiveStreamsOperatorsProcessor#classLoadingHack", "started": "12:24:36.956", "dependents": [532], "id": 248, "thread": "build-30"}, {"duration": 244, "stepId": "io.quarkus.deployment.steps.PreloadClassesBuildStep#preInit", "started": "12:24:36.953", "dependents": [532], "id": 245, "thread": "build-17"}, {"duration": 244, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#initFormAuth", "started": "12:24:36.960", "dependents": [532, 406, 521, 421, 520], "id": 254, "thread": "build-2"}, {"duration": 240, "stepId": "io.quarkus.devui.deployment.menu.ConfigurationProcessor#registerJsonRpcService", "started": "12:24:36.999", "dependents": [286, 450, 532, 365, 449, 454, 289], "id": 284, "thread": "build-31"}, {"duration": 238, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#bodyHandler", "started": "12:24:37.098", "dependents": [532, 522], "id": 302, "thread": "build-39"}, {"duration": 235, "stepId": "io.quarkus.smallrye.reactivemessaging.rabbitmq.deployment.SmallRyeReactiveMessagingRabbitMQProcessor#dynamicCredentials", "started": "12:24:36.975", "dependents": [472, 450, 532, 406, 449, 421, 454], "id": 260, "thread": "build-44"}, {"duration": 235, "stepId": "io.quarkus.deployment.logging.LoggingResourceProcessor#registerMetrics", "started": "12:24:36.976", "dependents": [383, 532], "id": 261, "thread": "build-46"}, {"duration": 229, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#ioThreadDetector", "started": "12:24:36.992", "dependents": [532, 280], "id": 267, "thread": "build-29"}, {"duration": 228, "stepId": "io.quarkus.deployment.logging.LoggingResourceProcessor#setupLoggingStaticInit", "started": "12:24:36.974", "dependents": [532], "id": 247, "thread": "build-5"}, {"duration": 227, "stepId": "io.quarkus.devui.deployment.DevUIProcessor#getAllExtensions", "started": "12:24:38.463", "dependents": [493, 480, 478, 492, 479], "id": 477, "thread": "build-59"}, {"duration": 222, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#addDefaultAuthFailureHandler", "started": "12:24:37.028", "dependents": [532, 522, 518], "id": 288, "thread": "build-67"}, {"duration": 220, "stepId": "io.quarkus.arc.deployment.ArcProcessor#registerBeans", "started": "12:24:37.857", "dependents": [429, 441, 436, 449, 432, 434, 454, 425, 430, 428, 445, 450, 439, 427, 437, 435, 431, 426, 458, 433], "id": 424, "thread": "build-37"}, {"duration": 219, "stepId": "io.quarkus.deployment.ide.IdeProcessor#detectRunningIdeProcesses", "started": "12:24:36.992", "dependents": [276], "id": 259, "thread": "build-54"}, {"duration": 214, "stepId": "io.quarkus.smallrye.openapi.deployment.SmallRyeOpenApiProcessor#classLoaderHack", "started": "12:24:36.989", "dependents": [532], "id": 250, "thread": "build-10"}, {"duration": 213, "stepId": "io.quarkus.rest.client.reactive.deployment.RestClientReactiveProcessor#setupAdditionalBeans", "started": "12:24:36.996", "dependents": [532, 406, 421], "id": 258, "thread": "build-23"}, {"duration": 206, "stepId": "io.quarkus.mutiny.deployment.MutinyProcessor#buildTimeInit", "started": "12:24:36.996", "dependents": [532], "id": 253, "thread": "build-48"}, {"duration": 206, "stepId": "io.quarkus.deployment.dev.io.NioThreadPoolDevModeProcessor#setupTCCL", "started": "12:24:36.996", "dependents": [532], "id": 249, "thread": "build-41"}, {"duration": 201, "stepId": "io.quarkus.devui.deployment.logstream.LogStreamProcessor#handler", "started": "12:24:37.021", "dependents": [383, 532], "id": 275, "thread": "build-11"}, {"duration": 198, "stepId": "io.quarkus.deployment.index.ApplicationArchiveBuildStep#build", "started": "12:24:37.372", "dependents": [314, 445, 530, 313, 406, 496, 315, 524, 348, 394, 467, 316], "id": 312, "thread": "build-37"}, {"duration": 193, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#setupAuthenticationMechanisms", "started": "12:24:37.027", "dependents": [532, 406, 522, 417, 421, 518, 420], "id": 266, "thread": "build-68"}, {"duration": 190, "stepId": "io.quarkus.deployment.steps.BannerProcessor#recordBanner", "started": "12:24:37.098", "dependents": [383, 532], "id": 298, "thread": "build-45"}, {"duration": 189, "stepId": "io.quarkus.deployment.steps.ClassPathSystemPropBuildStep#set", "started": "12:24:37.019", "dependents": [532], "id": 257, "thread": "build-62"}, {"duration": 186, "stepId": "io.quarkus.vertx.http.deployment.ManagementInterfaceSecurityProcessor#setupAuthenticationMechanisms", "started": "12:24:37.027", "dependents": [532, 406, 522, 421], "id": 263, "thread": "build-61"}, {"duration": 174, "stepId": "io.quarkus.devui.deployment.build.BuildMetricsDevUIProcessor#create", "started": "12:24:37.028", "dependents": [532], "id": 251, "thread": "build-58"}, {"duration": 163, "stepId": "io.quarkus.arc.deployment.ArcProcessor#validate", "started": "12:24:38.218", "dependents": [481, 473, 469, 466, 463, 530, 465, 474, 471, 468, 467, 462, 464], "id": 461, "thread": "build-23"}, {"duration": 160, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmCdiProcessor#generateJpaConfigBean", "started": "12:24:37.098", "dependents": [450, 532, 449, 454], "id": 291, "thread": "build-22"}, {"duration": 152, "stepId": "io.quarkus.resteasy.reactive.jackson.deployment.processor.ResteasyReactiveJacksonProcessor#handleFieldSecurity", "started": "12:24:39.054", "dependents": [506], "id": 505, "thread": "build-64"}, {"duration": 144, "stepId": "io.quarkus.deployment.steps.RuntimeConfigSetupBuildStep#setupRuntimeConfig", "started": "12:24:36.953", "dependents": [517, 494, 262, 532, 313, 268, 296, 518, 463, 445, 383, 516, 281, 270, 397, 291, 302, 457, 277, 455, 271, 521, 272, 527, 391, 444, 525, 522, 273, 417, 489, 456, 252, 526, 392, 303, 298, 308, 256], "id": 240, "thread": "build-25"}, {"duration": 143, "stepId": "io.quarkus.deployment.console.ConsoleProcessor#quitCommand", "started": "12:24:36.948", "dependents": [502], "id": 237, "thread": "build-24"}, {"duration": 141, "stepId": "io.quarkus.agroal.deployment.AgroalProcessor#generateDataSourceSupportBean", "started": "12:24:37.099", "dependents": [450, 532, 406, 449, 461, 421, 469, 454], "id": 285, "thread": "build-60"}, {"duration": 138, "stepId": "io.quarkus.deployment.steps.ConfigGenerationBuildStep#checkForBuildTimeConfigChange", "started": "12:24:37.098", "dependents": [532], "id": 281, "thread": "build-40"}, {"duration": 136, "stepId": "io.quarkus.deployment.steps.ConfigGenerationBuildStep#buildTimeRunTimeConfig", "started": "12:24:36.962", "dependents": [528, 472], "id": 242, "thread": "build-33"}, {"duration": 135, "stepId": "io.quarkus.narayana.jta.deployment.NarayanaJtaProcessor#build", "started": "12:24:37.585", "dependents": [528, 532, 406, 392, 456, 421], "id": 391, "thread": "build-40"}, {"duration": 134, "stepId": "io.quarkus.vertx.http.deployment.webjar.WebJarProcessor#processWebJarDevMode", "started": "12:24:38.690", "dependents": [494, 493, 532], "id": 492, "thread": "build-106"}, {"duration": 134, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#finalizeRouter", "started": "12:24:39.775", "dependents": [532, 525, 523, 526], "id": 522, "thread": "build-25"}, {"duration": 134, "stepId": "io.quarkus.vertx.http.deployment.devmode.NotFoundProcessor#routeNotFound", "started": "12:24:39.777", "dependents": [532], "id": 524, "thread": "build-236"}, {"duration": 130, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#vertxIntegration", "started": "12:24:36.948", "dependents": [508, 512, 509, 510], "id": 236, "thread": "build-26"}, {"duration": 129, "stepId": "io.quarkus.vertx.http.deployment.console.ConsoleProcessor#setupConsole", "started": "12:24:36.993", "dependents": [523, 526], "id": 243, "thread": "build-49"}, {"duration": 126, "stepId": "io.quarkus.datasource.deployment.DataSourcesExcludedFromHealthChecksProcessor#produceBean", "started": "12:24:37.098", "dependents": [450, 532, 449, 454], "id": 277, "thread": "build-4"}, {"duration": 123, "stepId": "io.quarkus.logging.json.deployment.LoggingJsonProcessor#setUpConsoleFormatter", "started": "12:24:37.098", "dependents": [383, 532], "id": 268, "thread": "build-24"}, {"duration": 123, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#cors", "started": "12:24:37.098", "dependents": [532, 522, 518], "id": 273, "thread": "build-13"}, {"duration": 123, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#eventLoopCount", "started": "12:24:37.098", "dependents": [532, 527], "id": 272, "thread": "build-56"}, {"duration": 123, "stepId": "io.quarkus.logging.json.deployment.LoggingJsonProcessor#setUpSyslogFormatter", "started": "12:24:37.098", "dependents": [383, 532], "id": 271, "thread": "build-15"}, {"duration": 123, "stepId": "io.quarkus.logging.json.deployment.LoggingJsonProcessor#setUpFileFormatter", "started": "12:24:37.098", "dependents": [383, 532], "id": 270, "thread": "build-26"}, {"duration": 122, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#initializeRouter", "started": "12:24:39.653", "dependents": [532, 522, 524], "id": 521, "thread": "build-150"}, {"duration": 122, "stepId": "io.quarkus.devui.deployment.DevUIProcessor#registerDevUiHandlers", "started": "12:24:39.103", "dependents": [532, 521, 520], "id": 511, "thread": "build-47"}, {"duration": 121, "stepId": "io.quarkus.deployment.steps.NativeImageConfigBuildStep#build", "started": "12:24:37.099", "dependents": [532], "id": 265, "thread": "build-25"}, {"duration": 120, "stepId": "io.quarkus.rest.client.reactive.deployment.RestClientReactiveProcessor#addMpClientEnricher", "started": "12:24:36.956", "dependents": [510], "id": 235, "thread": "build-22"}, {"duration": 115, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#setupPersistenceProvider", "started": "12:24:37.098", "dependents": [487, 532, 282], "id": 262, "thread": "build-35"}, {"duration": 115, "stepId": "io.quarkus.deployment.logging.LoggingResourceProcessor#setupLoggingRuntimeInit", "started": "12:24:37.585", "dependents": [528, 532, 525, 386, 390, 387], "id": 383, "thread": "build-29"}, {"duration": 115, "stepId": "io.quarkus.agroal.deployment.AgroalMetricsProcessor#registerMetrics", "started": "12:24:37.099", "dependents": [532], "id": 264, "thread": "build-66"}, {"duration": 113, "stepId": "io.quarkus.deployment.steps.ConfigDescriptionBuildStep#createConfigDescriptions", "started": "12:24:36.962", "dependents": [452, 453], "id": 234, "thread": "build-39"}, {"duration": 109, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#setMtlsCertificateRoleProperties", "started": "12:24:37.098", "dependents": [532], "id": 256, "thread": "build-57"}, {"duration": 108, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#defineJpaEntities", "started": "12:24:37.592", "dependents": [447, 528, 487, 385, 531, 399, 384, 403, 495, 394, 397], "id": 382, "thread": "build-31"}, {"duration": 104, "stepId": "io.quarkus.deployment.steps.ConfigGenerationBuildStep#releaseConfigOnShutdown", "started": "12:24:37.098", "dependents": [532], "id": 252, "thread": "build-47"}, {"duration": 104, "stepId": "io.quarkus.devui.deployment.build.BuildMetricsDevUIProcessor#additionalBeans", "started": "12:24:36.945", "dependents": [406, 421], "id": 222, "thread": "build-6"}, {"duration": 102, "stepId": "io.quarkus.deployment.console.ConsoleProcessor#helpCommand", "started": "12:24:36.989", "dependents": [502], "id": 238, "thread": "build-40"}, {"duration": 97, "stepId": "io.quarkus.vertx.http.deployment.StaticResourcesProcessor#collectStaticResources", "started": "12:24:37.027", "dependents": [489], "id": 244, "thread": "build-72"}, {"duration": 81, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#initBasicAuth", "started": "12:24:36.976", "dependents": [406, 417, 421, 420], "id": 228, "thread": "build-45"}, {"duration": 76, "stepId": "io.quarkus.devui.deployment.BuildTimeContentProcessor#createIndexHtmlTemplate", "started": "12:24:39.025", "dependents": [504], "id": 503, "thread": "build-47"}, {"duration": 75, "stepId": "io.quarkus.arc.deployment.devui.ArcDevModeApiProcessor#collectBeanInfo", "started": "12:24:38.382", "dependents": [475], "id": 474, "thread": "build-25"}, {"duration": 73, "stepId": "io.quarkus.devui.deployment.menu.ContinuousTestingProcessor#createJsonRPCService", "started": "12:24:36.931", "dependents": [286, 365, 289], "id": 140, "thread": "build-7"}, {"duration": 73, "stepId": "io.quarkiverse.caffeine.deployment.devui.CaffeineDevUIProcessor#createCard", "started": "12:24:36.989", "dependents": [476, 477], "id": 230, "thread": "build-13"}, {"duration": 73, "stepId": "io.quarkus.deployment.steps.RegisterForReflectionBuildStep#build", "started": "12:24:37.585", "dependents": [528, 513], "id": 378, "thread": "build-19"}, {"duration": 72, "stepId": "io.quarkus.arc.deployment.ArcProcessor#quarkusMain", "started": "12:24:36.977", "dependents": [406, 421, 229], "id": 219, "thread": "build-47"}, {"duration": 70, "stepId": "io.quarkus.smallrye.reactivemessaging.deployment.WiringProcessor#generateDocumentationItem", "started": "12:24:38.098", "dependents": [452, 453], "id": 451, "thread": "build-23"}, {"duration": 69, "stepId": "io.quarkus.vertx.deployment.VertxJsonProcessor#registerJacksonSerDeser", "started": "12:24:37.023", "dependents": [361], "id": 239, "thread": "build-35"}, {"duration": 67, "stepId": "io.quarkus.scheduler.deployment.SchedulerProcessor#autoAddScope", "started": "12:24:36.931", "dependents": [415], "id": 133, "thread": "build-4"}, {"duration": 65, "stepId": "io.quarkus.smallrye.reactivemessaging.deployment.SmallRyeReactiveMessagingProcessor#removalExclusions", "started": "12:24:36.959", "dependents": [461, 469], "id": 175, "thread": "build-9"}, {"duration": 62, "stepId": "io.quarkus.rest.client.reactive.deployment.RestClientReactiveProcessor#registerProvidersFromAnnotations", "started": "12:24:37.585", "dependents": [528, 406, 461, 469], "id": 377, "thread": "build-59"}, {"duration": 61, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveScanningProcessor#handleCustomAnnotatedMethods", "started": "12:24:37.620", "dependents": [380, 406, 421, 381], "id": 379, "thread": "build-66"}, {"duration": 61, "stepId": "io.quarkus.arc.deployment.SyntheticBeansProcessor#initRuntime", "started": "12:24:38.145", "dependents": [457, 487, 532, 455, 523, 526, 456, 458], "id": 454, "thread": "build-46"}, {"duration": 60, "stepId": "io.quarkus.agroal.deployment.AgroalProcessor#build", "started": "12:24:37.038", "dependents": [528, 265, 406, 392, 421, 264, 285], "id": 241, "thread": "build-74"}, {"duration": 58, "stepId": "io.quarkus.arc.deployment.ArcProcessor#initialize", "started": "12:24:37.796", "dependents": [422, 424, 474, 441], "id": 421, "thread": "build-37"}, {"duration": 57, "stepId": "io.quarkus.deployment.steps.ReflectiveHierarchyStep#build", "started": "12:24:39.223", "dependents": [528], "id": 513, "thread": "build-63"}, {"duration": 56, "stepId": "io.quarkus.scheduler.deployment.SchedulerProcessor#validateScheduledBusinessMethods", "started": "12:24:38.382", "dependents": [481], "id": 473, "thread": "build-31"}, {"duration": 56, "stepId": "io.quarkus.smallrye.reactivemessaging.deployment.SmallRyeReactiveMessagingProcessor#enableMetrics", "started": "12:24:36.962", "dependents": [421], "id": 161, "thread": "build-11"}, {"duration": 55, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveCDIProcessor#contextInjection", "started": "12:24:36.932", "dependents": [415, 412, 406, 421], "id": 98, "thread": "build-13"}, {"duration": 55, "stepId": "io.quarkus.arc.deployment.ConfigStaticInitBuildSteps#transformConfigProducer", "started": "12:24:37.002", "dependents": [421], "id": 227, "thread": "build-57"}, {"duration": 54, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#build_68c59e5d5fe4deeaa2b750dd2b2f234cee36c063", "started": "12:24:37.287", "dependents": [532, 443, 449, 521, 527, 454, 450, 522, 489, 523, 306, 526, 308], "id": 303, "thread": "build-46"}, {"duration": 51, "stepId": "io.quarkus.deployment.dev.testing.TestTracingProcessor#testConsoleCommand", "started": "12:24:37.585", "dependents": [502], "id": 373, "thread": "build-45"}, {"duration": 47, "stepId": "io.quarkus.devui.deployment.logstream.LogStreamProcessor#createJsonRPCService", "started": "12:24:37.021", "dependents": [286, 365, 289], "id": 231, "thread": "build-56"}, {"duration": 47, "stepId": "io.quarkus.smallrye.reactivemessaging.deployment.SmallRyeReactiveMessagingProcessor#build", "started": "12:24:38.098", "dependents": [528, 472, 450, 532, 449, 454], "id": 448, "thread": "build-15"}, {"duration": 46, "stepId": "io.quarkus.rest.client.reactive.jackson.deployment.RestClientReactiveJacksonProcessor#additionalProviders_3f333413be4c0802e30f75e67ce4dd421dc2e40b", "started": "12:24:37.029", "dependents": [508, 406, 512, 509, 510, 421], "id": 233, "thread": "build-4"}, {"duration": 46, "stepId": "io.quarkus.hibernate.orm.deployment.dev.HibernateOrmDevUIProcessor#additionalBeans", "started": "12:24:37.002", "dependents": [406, 421], "id": 221, "thread": "build-63"}, {"duration": 46, "stepId": "io.quarkus.resteasy.reactive.jackson.deployment.processor.ResteasyReactiveJacksonProcessor#beans", "started": "12:24:37.002", "dependents": [406, 421], "id": 218, "thread": "build-42"}, {"duration": 45, "stepId": "io.quarkus.smallrye.reactivemessaging.deployment.SmallRyeReactiveMessagingProcessor#beans", "started": "12:24:36.999", "dependents": [406, 421], "id": 216, "thread": "build-32"}, {"duration": 42, "stepId": "io.quarkus.arc.deployment.BeanArchiveProcessor#build", "started": "12:24:37.745", "dependents": [416, 415, 509, 510, 413, 441, 436, 408, 421, 410, 473, 434, 414, 407, 445, 409, 490, 435, 500, 411], "id": 406, "thread": "build-46"}, {"duration": 41, "stepId": "io.quarkus.devui.deployment.DevUIProcessor#findAllJsonRPCMethods", "started": "12:24:37.585", "dependents": [499, 491], "id": 365, "thread": "build-61"}, {"duration": 41, "stepId": "io.quarkus.netty.deployment.NettyProcessor#setNettyMachineId", "started": "12:24:37.031", "dependents": [532], "id": 232, "thread": "build-15"}, {"duration": 40, "stepId": "io.quarkus.devui.deployment.BuildTimeContentProcessor#createBuildTimeData", "started": "12:24:38.701", "dependents": [499, 503], "id": 480, "thread": "build-15"}, {"duration": 39, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#filterMultipleVertxInstancesWarning", "started": "12:24:37.014", "dependents": [383, 226], "id": 225, "thread": "build-60"}, {"duration": 38, "stepId": "io.quarkus.deployment.CollectionClassProcessor#setupCollectionClasses", "started": "12:24:36.992", "dependents": [528], "id": 199, "thread": "build-15"}, {"duration": 38, "stepId": "io.quarkus.deployment.steps.ConfigGenerationBuildStep#watchConfigFiles", "started": "12:24:36.936", "dependents": [447], "id": 75, "thread": "build-19"}, {"duration": 38, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmCdiProcessor#registerAnnotations", "started": "12:24:37.012", "dependents": [406, 421, 229], "id": 220, "thread": "build-28"}, {"duration": 37, "stepId": "io.quarkus.deployment.steps.ConfigGenerationBuildStep#generateMappings", "started": "12:24:37.584", "dependents": [528, 428, 465, 469, 433], "id": 362, "thread": "build-37"}, {"duration": 37, "stepId": "io.quarkus.scheduler.deployment.SchedulerProcessor#build", "started": "12:24:38.092", "dependents": [528, 450, 532, 449, 454], "id": 446, "thread": "build-31"}, {"duration": 36, "stepId": "io.quarkus.jackson.deployment.JacksonProcessor#generateCustomizer", "started": "12:24:37.585", "dependents": [406], "id": 361, "thread": "build-39"}, {"duration": 36, "stepId": "io.quarkus.rest.client.reactive.deployment.RestClientReactiveProcessor#addRestClientBeans", "started": "12:24:37.585", "dependents": [532, 406], "id": 360, "thread": "build-25"}, {"duration": 36, "stepId": "io.quarkus.deployment.dev.IsolatedDevModeMain$5$1@30cb0473", "started": "12:24:36.938", "dependents": [500, 421], "id": 74, "thread": "build-27"}, {"duration": 35, "stepId": "io.quarkus.vertx.deployment.VertxProcessor#unremovableBeans", "started": "12:24:36.924", "dependents": [461, 469], "id": 54, "thread": "build-9"}, {"duration": 35, "stepId": "io.quarkus.deployment.steps.ClassPathSystemPropBuildStep#produce", "started": "12:24:36.981", "dependents": [257], "id": 160, "thread": "build-51"}, {"duration": 35, "stepId": "io.quarkus.jaxrs.client.reactive.deployment.JaxrsClientReactiveProcessor#initializeStorkFilter", "started": "12:24:36.923", "dependents": [528, 406, 421, 316], "id": 53, "thread": "build-5"}, {"duration": 34, "stepId": "io.quarkus.deployment.DockerStatusProcessor#IsDockerWorking", "started": "12:24:36.924", "dependents": [386, 390, 387, 404], "id": 52, "thread": "build-12"}, {"duration": 33, "stepId": "io.quarkus.jsonp.deployment.JsonpProcessor#build", "started": "12:24:36.924", "dependents": [528, 532], "id": 48, "thread": "build-10"}, {"duration": 33, "stepId": "io.quarkus.deployment.steps.CompiledJavaVersionBuildStep#compiledJavaVersion", "started": "12:24:36.944", "dependents": [500], "id": 79, "thread": "build-3"}, {"duration": 31, "stepId": "io.quarkus.resteasy.reactive.common.deployment.ResteasyReactiveCommonProcessor#scanResources", "started": "12:24:37.589", "dependents": [506, 357, 514, 510, 356, 363, 421, 414, 355, 358, 490, 359, 375, 500, 379], "id": 354, "thread": "build-53"}, {"duration": 31, "stepId": "io.quarkus.deployment.ide.IdeProcessor#detectIdeFiles", "started": "12:24:37.012", "dependents": [276], "id": 211, "thread": "build-55"}, {"duration": 31, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#frameworkRoot", "started": "12:24:36.958", "dependents": [117, 494, 118, 501, 521, 518, 503, 423, 511, 522, 471, 519, 499, 480, 170, 243], "id": 111, "thread": "build-23"}, {"duration": 30, "stepId": "io.quarkus.smallrye.openapi.deployment.SmallRyeOpenApiProcessor#addAutoFilters", "started": "12:24:37.800", "dependents": [529], "id": 420, "thread": "build-45"}, {"duration": 30, "stepId": "io.quarkus.deployment.steps.ConfigGenerationBuildStep#generateBuilders", "started": "12:24:38.389", "dependents": [528], "id": 472, "thread": "build-66"}, {"duration": 30, "stepId": "io.quarkus.devservices.postgresql.deployment.PostgresqlDevServicesProcessor#setupPostgres", "started": "12:24:36.958", "dependents": [390], "id": 105, "thread": "build-28"}, {"duration": 29, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#shouldNotRemoveHttpServerOptionsCustomizers", "started": "12:24:36.931", "dependents": [461, 469], "id": 56, "thread": "build-2"}, {"duration": 28, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#recordableConstructor", "started": "12:24:36.987", "dependents": [532], "id": 157, "thread": "build-3"}, {"duration": 28, "stepId": "io.quarkus.deployment.steps.CapabilityAggregationStep#provideCapabilities", "started": "12:24:36.981", "dependents": [177], "id": 151, "thread": "build-50"}, {"duration": 28, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#securityExceptionMappers", "started": "12:24:37.023", "dependents": [381], "id": 224, "thread": "build-66"}, {"duration": 27, "stepId": "io.quarkus.rest.client.reactive.deployment.RestClientReactiveProcessor#setUpDefaultMediaType", "started": "12:24:36.970", "dependents": [510], "id": 131, "thread": "build-42"}, {"duration": 27, "stepId": "io.quarkus.smallrye.openapi.deployment.devui.OpenApiDevUIProcessor#pages", "started": "12:24:36.994", "dependents": [476, 477], "id": 170, "thread": "build-58"}, {"duration": 27, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#registerAuthMechanismSelectionInterceptor", "started": "12:24:37.585", "dependents": [532, 431, 352, 350], "id": 349, "thread": "build-11"}, {"duration": 26, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#build_9d6b7122fb368970c50c3a870d1f672392cd8afb", "started": "12:24:37.000", "dependents": [528, 265], "id": 182, "thread": "build-61"}, {"duration": 26, "stepId": "io.quarkus.deployment.pkg.steps.JarResultBuildStep#outputTarget", "started": "12:24:36.962", "dependents": [153, 529, 154], "id": 109, "thread": "build-40"}, {"duration": 26, "stepId": "io.quarkus.resteasy.reactive.common.deployment.ResteasyReactiveCommonProcessor#handleApplication", "started": "12:24:37.599", "dependents": [366, 514, 380, 512, 510, 368, 367, 370, 381, 528, 490, 500, 371, 369, 372], "id": 364, "thread": "build-44"}, {"duration": 25, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#logging", "started": "12:24:36.931", "dependents": [94], "id": 45, "thread": "build-16"}, {"duration": 24, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#serverSerializers", "started": "12:24:39.212", "dependents": [528, 532, 514], "id": 512, "thread": "build-226"}, {"duration": 24, "stepId": "io.quarkus.scheduler.deployment.SchedulerProcessor#beans", "started": "12:24:37.025", "dependents": [406, 421], "id": 223, "thread": "build-3"}, {"duration": 23, "stepId": "io.quarkus.deployment.steps.MainClassBuildStep#mainClassBuildStep", "started": "12:24:37.585", "dependents": [530], "id": 348, "thread": "build-22"}, {"duration": 22, "stepId": "io.quarkus.devui.deployment.menu.ConfigurationProcessor#registerConfigs", "started": "12:24:38.169", "dependents": [532], "id": 453, "thread": "build-31"}, {"duration": 22, "stepId": "io.quarkus.hibernate.orm.deployment.metrics.HibernateOrmMetricsProcessor#metrics", "started": "12:24:37.214", "dependents": [532], "id": 282, "thread": "build-46"}, {"duration": 22, "stepId": "io.quarkus.arc.deployment.CommandLineArgumentsProcessor#commandLineArgs", "started": "12:24:37.021", "dependents": [450, 406, 449, 421, 454], "id": 213, "thread": "build-64"}, {"duration": 21, "stepId": "io.quarkus.scheduler.deployment.SchedulerProcessor#unremovableBeans", "started": "12:24:36.924", "dependents": [461, 469], "id": 29, "thread": "build-11"}, {"duration": 21, "stepId": "io.quarkus.smallrye.openapi.deployment.SmallRyeOpenApiProcessor#configFiles", "started": "12:24:36.989", "dependents": [447], "id": 154, "thread": "build-52"}, {"duration": 21, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#generateCustomProducer", "started": "12:24:37.620", "dependents": [406, 421], "id": 375, "thread": "build-46"}, {"duration": 21, "stepId": "io.quarkus.deployment.pkg.steps.FileSystemResourcesBuildStep#notNormalMode", "started": "12:24:36.989", "dependents": [], "id": 153, "thread": "build-55"}, {"duration": 21, "stepId": "io.quarkus.rest.client.reactive.deployment.RestClientReactiveProcessor#setupRequestCollectingFilter", "started": "12:24:37.022", "dependents": [380], "id": 215, "thread": "build-69"}, {"duration": 21, "stepId": "io.quarkus.arc.deployment.ArcProcessor#setupExecutor", "started": "12:24:37.287", "dependents": [532], "id": 301, "thread": "build-37"}, {"duration": 21, "stepId": "io.quarkus.scheduler.deployment.devui.SchedulerDevUIProcessor#rpcProvider", "started": "12:24:36.923", "dependents": [365, 289], "id": 26, "thread": "build-6"}, {"duration": 21, "stepId": "io.quarkus.resteasy.reactive.jackson.deployment.processor.ResteasyReactiveJacksonProcessor#exceptionMappers", "started": "12:24:36.924", "dependents": [381], "id": 30, "thread": "build-15"}, {"duration": 20, "stepId": "io.quarkus.vertx.http.deployment.devmode.ArcDevProcessor#registerRoutes", "started": "12:24:38.382", "dependents": [532, 481, 521, 524, 520], "id": 471, "thread": "build-59"}, {"duration": 20, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#fileHandling", "started": "12:24:36.930", "dependents": [512, 509, 510], "id": 41, "thread": "build-22"}, {"duration": 20, "stepId": "io.quarkus.smallrye.reactivemessaging.deployment.SmallRyeReactiveMessagingProcessor#devmodeSupport", "started": "12:24:37.585", "dependents": [406, 421], "id": 347, "thread": "build-14"}, {"duration": 20, "stepId": "io.quarkus.scheduler.deployment.SchedulerProcessor#metrics", "started": "12:24:36.958", "dependents": [421], "id": 82, "thread": "build-29"}, {"duration": 19, "stepId": "io.quarkus.jaxrs.client.reactive.deployment.JaxrsClientReactiveProcessor#registerInvocationCallbacks", "started": "12:24:37.585", "dependents": [532], "id": 346, "thread": "build-23"}, {"duration": 19, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#runtimeConfiguration", "started": "12:24:39.619", "dependents": [517, 532], "id": 516, "thread": "build-25"}, {"duration": 19, "stepId": "io.quarkus.resteasy.reactive.common.deployment.ResteasyReactiveCommonProcessor#checkMixingStacks", "started": "12:24:37.027", "dependents": [523, 526], "id": 217, "thread": "build-50"}, {"duration": 19, "stepId": "io.quarkus.arc.deployment.ConfigBuildStep#registerConfigRootsAsBeans", "started": "12:24:36.989", "dependents": [450, 449, 454], "id": 147, "thread": "build-28"}, {"duration": 19, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveCDIProcessor#beanDefiningAnnotations", "started": "12:24:36.961", "dependents": [406, 421, 229], "id": 90, "thread": "build-10"}, {"duration": 19, "stepId": "io.quarkus.hibernate.orm.deployment.dev.HibernateOrmDevUIProcessor#create", "started": "12:24:36.926", "dependents": [476, 477], "id": 28, "thread": "build-17"}, {"duration": 18, "stepId": "io.quarkus.resteasy.reactive.jackson.deployment.processor.ResteasyReactiveJacksonProcessor#resolveRolesAllowedConfigExpressions", "started": "12:24:37.584", "dependents": [463, 450, 532, 449, 454], "id": 344, "thread": "build-46"}, {"duration": 18, "stepId": "io.quarkus.smallrye.openapi.deployment.SmallRyeOpenApiProcessor#registerOpenApiSchemaClassesForReflection", "started": "12:24:37.800", "dependents": [528, 513], "id": 419, "thread": "build-40"}, {"duration": 18, "stepId": "io.quarkus.deployment.steps.DevModeBuildStep#watchChanges", "started": "12:24:36.960", "dependents": [447], "id": 83, "thread": "build-38"}, {"duration": 18, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveScanningProcessor#cacheControlSupport", "started": "12:24:37.002", "dependents": [500], "id": 169, "thread": "build-18"}, {"duration": 17, "stepId": "io.quarkus.deployment.logging.LoggingResourceProcessor#setMinLevelForInitialConfigurator", "started": "12:24:36.962", "dependents": [532], "id": 86, "thread": "build-12"}, {"duration": 17, "stepId": "io.quarkus.smallrye.openapi.deployment.SmallRyeOpenApiProcessor#handler", "started": "12:24:39.634", "dependents": [532, 519, 524], "id": 518, "thread": "build-150"}, {"duration": 17, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#additionalBeans", "started": "12:24:37.002", "dependents": [406, 421], "id": 162, "thread": "build-56"}, {"duration": 17, "stepId": "io.quarkus.jdbc.postgresql.deployment.JDBCPostgreSQLProcessor#devDbHandler", "started": "12:24:37.014", "dependents": [390], "id": 203, "thread": "build-43"}, {"duration": 17, "stepId": "io.quarkus.smallrye.reactivemessaging.deployment.SmallRyeReactiveMessagingProcessor#duplicatedContextSupport", "started": "12:24:37.585", "dependents": [406, 421], "id": 345, "thread": "build-15"}, {"duration": 16, "stepId": "io.quarkus.deployment.steps.ConfigGenerationBuildStep#setupConfigOverride", "started": "12:24:37.012", "dependents": [], "id": 190, "thread": "build-65"}, {"duration": 16, "stepId": "io.quarkus.smallrye.context.deployment.SmallRyeContextPropagationProcessor#build", "started": "12:24:37.287", "dependents": [450, 532, 449, 454], "id": 300, "thread": "build-12"}, {"duration": 16, "stepId": "io.quarkus.redis.deployment.client.RedisClientProcessor#init", "started": "12:24:38.098", "dependents": [447, 450, 532, 449, 454], "id": 445, "thread": "build-46"}, {"duration": 16, "stepId": "io.quarkus.arc.deployment.ConfigStaticInitBuildSteps#registerBeans", "started": "12:24:37.011", "dependents": [406, 421], "id": 191, "thread": "build-52"}, {"duration": 16, "stepId": "io.quarkus.netty.deployment.NettyProcessor#build", "started": "12:24:37.008", "dependents": [528, 265], "id": 183, "thread": "build-27"}, {"duration": 16, "stepId": "io.quarkus.jackson.deployment.JacksonProcessor#register", "started": "12:24:37.585", "dependents": [528, 406, 513, 421], "id": 343, "thread": "build-66"}, {"duration": 16, "stepId": "io.quarkus.smallrye.reactivemessaging.deployment.WiringProcessor#extractComponents", "started": "12:24:38.078", "dependents": [452, 453, 481, 448, 442, 440], "id": 439, "thread": "build-46"}, {"duration": 15, "stepId": "io.quarkus.smallrye.openapi.deployment.SmallRyeOpenApiProcessor#registerAnnotatedUserDefinedRuntimeFilters", "started": "12:24:37.800", "dependents": [528, 450, 532, 449, 454], "id": 418, "thread": "build-25"}, {"duration": 15, "stepId": "io.quarkus.smallrye.openapi.deployment.SmallRyeOpenApiProcessor#registerAutoSecurityFilter", "started": "12:24:37.800", "dependents": [450, 532, 449, 454], "id": 417, "thread": "build-31"}, {"duration": 15, "stepId": "io.quarkus.smallrye.reactivemessaging.deployment.WiringProcessor#discoverConnectors", "started": "12:24:38.078", "dependents": [451, 442], "id": 437, "thread": "build-15"}, {"duration": 15, "stepId": "io.quarkus.swaggerui.deployment.SwaggerUiProcessor#feature", "started": "12:24:36.978", "dependents": [532], "id": 124, "thread": "build-48"}, {"duration": 15, "stepId": "io.quarkus.deployment.dev.HotDeploymentWatchedFileBuildStep#setupWatchedFileHotDeployment", "started": "12:24:38.115", "dependents": [523, 526], "id": 447, "thread": "build-45"}, {"duration": 15, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ObservabilityProcessor#preAuthFailureFilter", "started": "12:24:39.619", "dependents": [532, 522, 518], "id": 515, "thread": "build-236"}, {"duration": 14, "stepId": "io.quarkus.arc.deployment.BuildTimeEnabledProcessor#buildExclusions", "started": "12:24:37.599", "dependents": [416], "id": 351, "thread": "build-68"}, {"duration": 14, "stepId": "io.quarkus.hibernate.orm.deployment.GraalVMFeatures#registerJdbcArrayTypesForReflection", "started": "12:24:37.023", "dependents": [528], "id": 207, "thread": "build-7"}, {"duration": 14, "stepId": "io.quarkus.deployment.steps.CombinedIndexBuildStep#build", "started": "12:24:37.570", "dependents": [318, 320, 328, 399, 413, 389, 370, 364, 362, 344, 378, 373, 329, 348, 339, 317, 433, 416, 506, 347, 336, 319, 391, 331, 377, 335, 349, 428, 321, 372, 366, 496, 368, 367, 497, 421, 337, 350, 346, 466, 381, 383, 323, 345, 437, 513, 371, 338, 324, 369, 379, 327, 333, 341, 332, 322, 325, 394, 360, 361, 385, 343, 365, 334, 326, 330, 340, 353], "id": 316, "thread": "build-39"}, {"duration": 14, "stepId": "io.quarkus.resteasy.reactive.jackson.deployment.processor.ResteasyReactiveJacksonProcessor#jsonDefault", "started": "12:24:37.006", "dependents": [500], "id": 164, "thread": "build-64"}, {"duration": 14, "stepId": "io.quarkus.hibernate.orm.panache.deployment.PanacheHibernateResourceProcessor#build", "started": "12:24:38.996", "dependents": [530, 498], "id": 497, "thread": "build-64"}, {"duration": 14, "stepId": "io.quarkus.arc.deployment.ArcProcessor#registerContextPropagation", "started": "12:24:36.976", "dependents": [287], "id": 113, "thread": "build-32"}, {"duration": 14, "stepId": "io.quarkus.vertx.deployment.VertxProcessor#autoAddScope", "started": "12:24:36.976", "dependents": [415], "id": 112, "thread": "build-41"}, {"duration": 14, "stepId": "io.quarkus.devui.deployment.DevUIProcessor#additionalBean", "started": "12:24:37.240", "dependents": [406, 421, 316], "id": 289, "thread": "build-46"}, {"duration": 14, "stepId": "io.quarkus.deployment.steps.CapabilityAggregationStep#aggregateCapabilities", "started": "12:24:37.010", "dependents": [262, 514, 180, 399, 421, 473, 529, 204, 381, 344, 378, 513, 390, 393, 223, 401, 178, 285, 291, 277, 510, 210, 403, 332, 192, 305, 187, 527, 217, 394, 186, 241, 349, 419, 360, 263, 244, 515, 188, 500, 392, 189, 266, 353], "id": 177, "thread": "build-4"}, {"duration": 14, "stepId": "io.quarkus.arc.deployment.devui.ArcDevUIProcessor#createJsonRPCService", "started": "12:24:37.021", "dependents": [365, 289], "id": 205, "thread": "build-70"}, {"duration": 14, "stepId": "io.quarkus.resteasy.reactive.jackson.deployment.processor.ResteasyReactiveJacksonProcessor#reflection", "started": "12:24:37.028", "dependents": [528], "id": 212, "thread": "build-51"}, {"duration": 14, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#hotDeploymentWatchedFiles", "started": "12:24:36.962", "dependents": [447], "id": 80, "thread": "build-15"}, {"duration": 13, "stepId": "io.quarkus.vertx.deployment.VertxProcessor#registerVerticleClasses", "started": "12:24:37.585", "dependents": [528], "id": 339, "thread": "build-67"}, {"duration": 13, "stepId": "io.quarkus.arc.deployment.HotDeploymentConfigBuildStep#startup", "started": "12:24:36.977", "dependents": [123], "id": 116, "thread": "build-15"}, {"duration": 13, "stepId": "io.quarkus.arc.deployment.SyntheticBeansProcessor#initRegular", "started": "12:24:38.145", "dependents": [458], "id": 450, "thread": "build-45"}, {"duration": 13, "stepId": "io.quarkus.deployment.recording.substitutions.AdditionalSubstitutionsBuildStep#additionalSubstitutions", "started": "12:24:37.031", "dependents": [532], "id": 214, "thread": "build-73"}, {"duration": 13, "stepId": "io.quarkus.arc.deployment.staticmethods.InterceptedStaticMethodsProcessor#collectInterceptedStaticMethods", "started": "12:24:38.078", "dependents": [485, 441, 461, 469], "id": 436, "thread": "build-45"}, {"duration": 13, "stepId": "io.quarkus.narayana.jta.deployment.NarayanaJtaProcessor#unremovableBean", "started": "12:24:36.971", "dependents": [461, 469], "id": 97, "thread": "build-31"}, {"duration": 13, "stepId": "io.quarkus.scheduler.deployment.SchedulerProcessor#collectScheduledMethods_84ea631eea52cbbcaee3e56019e68e7826861add", "started": "12:24:38.078", "dependents": [446, 473, 438], "id": 435, "thread": "build-23"}, {"duration": 12, "stepId": "io.quarkus.hibernate.orm.deployment.GraalVMFeatures#registerGeneratorClassesForReflections", "started": "12:24:37.006", "dependents": [528], "id": 158, "thread": "build-7"}, {"duration": 12, "stepId": "io.quarkus.arc.deployment.BuildTimeEnabledProcessor#unlessBuildProperty", "started": "12:24:37.586", "dependents": [351, 364, 342], "id": 340, "thread": "build-68"}, {"duration": 12, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#preinitializeRouter", "started": "12:24:37.340", "dependents": [450, 532, 449, 521, 454], "id": 308, "thread": "build-37"}, {"duration": 12, "stepId": "io.quarkus.arc.deployment.BuildTimeEnabledProcessor#ifBuildProperty", "started": "12:24:37.586", "dependents": [351, 364, 342], "id": 338, "thread": "build-44"}, {"duration": 12, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#registerVerticleClasses", "started": "12:24:37.585", "dependents": [528], "id": 336, "thread": "build-60"}, {"duration": 12, "stepId": "io.quarkus.smallrye.reactivemessaging.deployment.devui.ReactiveMessagingDevUIProcessor#createJsonRPCServiceForCache", "started": "12:24:36.958", "dependents": [365, 289], "id": 70, "thread": "build-32"}, {"duration": 12, "stepId": "io.quarkus.devui.deployment.logstream.LogStreamProcessor#additionalBean", "started": "12:24:36.956", "dependents": [406, 421], "id": 67, "thread": "build-35"}, {"duration": 12, "stepId": "io.quarkus.jackson.deployment.JacksonProcessor#supportMixins", "started": "12:24:37.585", "dependents": [528, 450, 532, 449, 454], "id": 337, "thread": "build-4"}, {"duration": 12, "stepId": "io.quarkus.smallrye.openapi.deployment.SmallRyeOpenApiProcessor#smallryeOpenApiIndex", "started": "12:24:37.787", "dependents": [418, 417, 529, 419, 420], "id": 416, "thread": "build-19"}, {"duration": 12, "stepId": "io.quarkus.arc.deployment.BuildTimeEnabledProcessor#ifBuildProfile", "started": "12:24:37.586", "dependents": [351, 364, 342], "id": 341, "thread": "build-34"}, {"duration": 12, "stepId": "io.quarkus.datasource.deployment.devservices.DevServicesDatasourceProcessor#launchDatabases", "started": "12:24:37.701", "dependents": [400, 404], "id": 390, "thread": "build-66"}, {"duration": 12, "stepId": "io.quarkus.redis.deployment.client.RedisDatasourceProcessor#detectUsage", "started": "12:24:38.078", "dependents": [445, 444], "id": 434, "thread": "build-31"}, {"duration": 12, "stepId": "io.quarkus.swaggerui.deployment.SwaggerUiProcessor#registerSwaggerUiHandler", "started": "12:24:38.824", "dependents": [532, 521, 520], "id": 494, "thread": "build-47"}, {"duration": 12, "stepId": "io.quarkus.arc.deployment.ArcProcessor#launchMode", "started": "12:24:36.957", "dependents": [406, 421], "id": 69, "thread": "build-34"}, {"duration": 12, "stepId": "io.quarkus.deployment.steps.ThreadPoolSetup#createExecutor", "started": "12:24:37.274", "dependents": [299, 297, 532, 301, 522, 300, 303], "id": 296, "thread": "build-22"}, {"duration": 12, "stepId": "io.quarkus.jdbc.postgresql.deployment.JDBCPostgreSQLProcessor#registerServiceBinding", "started": "12:24:37.025", "dependents": [390, 241], "id": 210, "thread": "build-18"}, {"duration": 11, "stepId": "io.quarkus.deployment.ide.IdeProcessor#effectiveIde", "started": "12:24:37.211", "dependents": [314, 480, 307, 279], "id": 276, "thread": "build-23"}, {"duration": 11, "stepId": "io.quarkus.arc.deployment.BuildTimeEnabledProcessor#unlessBuildProfile", "started": "12:24:37.586", "dependents": [351, 364, 342], "id": 335, "thread": "build-13"}, {"duration": 11, "stepId": "io.quarkus.jaxrs.client.reactive.deployment.JaxrsClientReactiveProcessor#setupClientProxies", "started": "12:24:39.212", "dependents": [528, 530, 532, 513], "id": 510, "thread": "build-106"}, {"duration": 11, "stepId": "io.quarkus.deployment.dev.testing.TestTracingProcessor#startTesting", "started": "12:24:37.342", "dependents": [383, 523, 526], "id": 309, "thread": "build-12"}, {"duration": 11, "stepId": "io.quarkus.rest.client.reactive.jackson.deployment.RestClientReactiveJacksonProcessor#additionalProviders_d467f0796ff6c3d28e57d6f18c92f27cf1d4298e", "started": "12:24:36.932", "dependents": [377], "id": 25, "thread": "build-20"}, {"duration": 11, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#configurationDescriptorBuilding", "started": "12:24:37.724", "dependents": [447, 532, 531, 396, 399, 403, 395, 398, 401, 397], "id": 394, "thread": "build-66"}, {"duration": 10, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveScanningProcessor#defaultUnwrappedException", "started": "12:24:36.977", "dependents": [381], "id": 100, "thread": "build-3"}, {"duration": 10, "stepId": "io.quarkus.arc.deployment.ConfigBuildStep#additionalBeans", "started": "12:24:36.977", "dependents": [406, 421], "id": 101, "thread": "build-27"}, {"duration": 10, "stepId": "io.quarkus.deployment.steps.CurateOutcomeBuildStep#removeResources", "started": "12:24:36.981", "dependents": [530], "id": 121, "thread": "build-43"}, {"duration": 10, "stepId": "io.quarkus.deployment.dev.testing.TestTracingProcessor#sharedStateListener", "started": "12:24:36.935", "dependents": [309], "id": 31, "thread": "build-26"}, {"duration": 10, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveScanningProcessor#compressionSupport", "started": "12:24:36.987", "dependents": [500], "id": 130, "thread": "build-31"}, {"duration": 10, "stepId": "io.quarkus.arc.deployment.UnremovableAnnotationsProcessor#unremovableBeans", "started": "12:24:37.014", "dependents": [461, 469], "id": 176, "thread": "build-67"}, {"duration": 10, "stepId": "io.quarkus.devui.deployment.DevUIProcessor#createJsonRpcRouter", "started": "12:24:38.782", "dependents": [532], "id": 491, "thread": "build-47"}, {"duration": 10, "stepId": "io.quarkus.vertx.deployment.VertxProcessor#collectEventConsumers", "started": "12:24:38.078", "dependents": [443, 458], "id": 432, "thread": "build-25"}, {"duration": 10, "stepId": "io.quarkus.smallrye.context.deployment.SmallRyeContextPropagationProcessor#transformInjectionPoint", "started": "12:24:36.999", "dependents": [421], "id": 150, "thread": "build-4"}, {"duration": 10, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#responseHeaderSupport", "started": "12:24:36.950", "dependents": [500], "id": 60, "thread": "build-31"}, {"duration": 10, "stepId": "io.quarkus.arc.deployment.ConfigBuildStep#registerConfigMappingsBean", "started": "12:24:38.079", "dependents": [458], "id": 433, "thread": "build-61"}, {"duration": 10, "stepId": "io.quarkus.arc.deployment.ShutdownBuildSteps#unremovableBeans", "started": "12:24:36.959", "dependents": [461, 469], "id": 68, "thread": "build-5"}, {"duration": 10, "stepId": "io.quarkus.vertx.deployment.VertxProcessor#build", "started": "12:24:38.088", "dependents": [528, 457, 445, 532, 523, 526, 444], "id": 443, "thread": "build-39"}, {"duration": 9, "stepId": "io.quarkus.resteasy.reactive.common.deployment.ResteasyReactiveCommonProcessor#deprioritizeLegacyProviders", "started": "12:24:36.948", "dependents": [512, 510], "id": 47, "thread": "build-29"}, {"duration": 9, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveScanningProcessor#scanForParamConverters_dcdfdd2a310a09abe5ee3f0ed2b2bc49f36f3d07", "started": "12:24:37.634", "dependents": [528, 514, 406, 500, 421], "id": 376, "thread": "build-37"}, {"duration": 9, "stepId": "io.quarkus.resteasy.reactive.common.deployment.JaxrsMethodsProcessor#jaxrsMethods", "started": "12:24:36.992", "dependents": [327], "id": 137, "thread": "build-57"}, {"duration": 9, "stepId": "io.quarkus.deployment.logging.LoggingResourceProcessor#setUpDarkeningDefault", "started": "12:24:36.961", "dependents": [472], "id": 71, "thread": "build-31"}, {"duration": 9, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveScanningProcessor#scanForFeatures", "started": "12:24:37.625", "dependents": [514, 374], "id": 370, "thread": "build-25"}, {"duration": 9, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveScanningProcessor#asyncSupport", "started": "12:24:37.012", "dependents": [500], "id": 165, "thread": "build-35"}, {"duration": 9, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#responseStatusSupport", "started": "12:24:37.002", "dependents": [500], "id": 155, "thread": "build-60"}, {"duration": 9, "stepId": "io.quarkus.jdbc.postgresql.deployment.JDBCPostgreSQLProcessor#configureAgroalConnection", "started": "12:24:37.025", "dependents": [406, 421], "id": 204, "thread": "build-9"}, {"duration": 9, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveScanningProcessor#scanForDynamicFeatures", "started": "12:24:37.625", "dependents": [514, 374], "id": 371, "thread": "build-15"}, {"duration": 9, "stepId": "io.quarkus.arc.deployment.StartupBuildSteps#addScope", "started": "12:24:36.996", "dependents": [415], "id": 143, "thread": "build-35"}, {"duration": 9, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#detectBasicAuthImplicitlyRequired", "started": "12:24:38.078", "dependents": [532], "id": 431, "thread": "build-39"}, {"duration": 9, "stepId": "io.quarkus.vertx.deployment.VertxProcessor#registerBean", "started": "12:24:37.012", "dependents": [406, 421], "id": 168, "thread": "build-66"}, {"duration": 9, "stepId": "io.quarkus.panache.common.deployment.PanacheHibernateCommonResourceProcessor#findEntityClasses", "started": "12:24:37.701", "dependents": [496], "id": 389, "thread": "build-29"}, {"duration": 9, "stepId": "io.quarkus.devui.deployment.welcome.WelcomeProcessor#createWelcomePages", "started": "12:24:38.691", "dependents": [480], "id": 479, "thread": "build-231"}, {"duration": 9, "stepId": "io.quarkus.deployment.steps.ReflectiveHierarchyStep#ignoreJavaClassWarnings", "started": "12:24:36.948", "dependents": [513], "id": 51, "thread": "build-23"}, {"duration": 9, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#build_7a4403d699506d83ac39616f3c11e5e1b448d863", "started": "12:24:37.735", "dependents": [532, 483], "id": 403, "thread": "build-19"}, {"duration": 9, "stepId": "io.quarkus.arc.deployment.ShutdownBuildSteps#addScope", "started": "12:24:37.001", "dependents": [415], "id": 152, "thread": "build-62"}, {"duration": 9, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveScanningProcessor#scanForContextResolvers", "started": "12:24:37.625", "dependents": [528, 514, 406, 421, 507], "id": 372, "thread": "build-11"}, {"duration": 9, "stepId": "io.quarkus.jackson.deployment.JacksonProcessor#jacksonSupport", "started": "12:24:37.585", "dependents": [450, 532, 449, 454], "id": 334, "thread": "build-35"}, {"duration": 9, "stepId": "io.quarkus.smallrye.openapi.deployment.SmallRyeOpenApiProcessor#additionalBean", "started": "12:24:36.971", "dependents": [406, 421], "id": 88, "thread": "build-34"}, {"duration": 9, "stepId": "io.quarkus.arc.deployment.devui.ArcDevUIProcessor#registerMonitoringComponents", "started": "12:24:37.050", "dependents": [406, 421], "id": 229, "thread": "build-47"}, {"duration": 8, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmCdiProcessor#generateDataSourceBeans", "started": "12:24:37.735", "dependents": [450, 532, 406, 449, 421, 454], "id": 401, "thread": "build-59"}, {"duration": 8, "stepId": "io.quarkus.arc.deployment.ArcProcessor#registerSyntheticObservers", "started": "12:24:38.207", "dependents": [528, 460, 481, 461, 469, 459], "id": 458, "thread": "build-15"}, {"duration": 8, "stepId": "io.quarkus.swaggerui.deployment.SwaggerUiProcessor#brandingFiles", "started": "12:24:36.989", "dependents": [447], "id": 132, "thread": "build-56"}, {"duration": 8, "stepId": "io.quarkus.arc.deployment.ConfigBuildStep#generateConfigProperties", "started": "12:24:37.585", "dependents": [528, 428, 465, 469, 433], "id": 333, "thread": "build-56"}, {"duration": 8, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#registerHibernateOrmMetadataForCoreDialects", "started": "12:24:36.983", "dependents": [394], "id": 120, "thread": "build-35"}, {"duration": 8, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveVertxWebSocketIntegrationProcessor#scanner", "started": "12:24:37.028", "dependents": [500], "id": 209, "thread": "build-74"}, {"duration": 8, "stepId": "io.quarkus.arc.deployment.TestsAsBeansProcessor#testAnnotations", "started": "12:24:36.953", "dependents": [406, 421, 229], "id": 66, "thread": "build-11"}, {"duration": 8, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#transformEndpoints", "started": "12:24:37.787", "dependents": [421], "id": 414, "thread": "build-37"}, {"duration": 8, "stepId": "io.quarkus.deployment.ExtensionLoader#config", "started": "12:24:36.927", "dependents": [494, 532, 313, 111, 386, 399, 105, 492, 82, 423, 83, 516, 310, 254, 390, 270, 431, 293, 291, 277, 58, 481, 418, 448, 109, 469, 391, 282, 161, 80, 511, 530, 471, 252, 526, 398, 303, 276, 269, 86, 262, 475, 234, 529, 296, 242, 430, 131, 445, 383, 183, 281, 312, 229, 482, 121, 457, 510, 327, 73, 521, 72, 394, 444, 260, 525, 334, 523, 273, 500, 266, 170, 353, 308, 247, 261, 113, 517, 406, 268, 228, 364, 304, 78, 124, 362, 84, 89, 92, 348, 397, 302, 295, 455, 377, 290, 349, 412, 465, 156, 91, 489, 456, 392, 298, 256, 264, 514, 446, 315, 421, 473, 93, 518, 442, 292, 130, 107, 474, 387, 420, 104, 108, 472, 271, 147, 272, 434, 309, 527, 154, 241, 360, 263, 522, 417, 411, 250], "id": 15, "thread": "build-18"}, {"duration": 8, "stepId": "io.quarkus.smallrye.reactivemessaging.deployment.ReactiveMessagingMethodsProcessor#reactiveMessagingMethods", "started": "12:24:36.953", "dependents": [327], "id": 65, "thread": "build-15"}, {"duration": 8, "stepId": "io.quarkus.jdbc.postgresql.deployment.JDBCPostgreSQLProcessor#registerDriver", "started": "12:24:36.971", "dependents": [241], "id": 85, "thread": "build-35"}, {"duration": 8, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveScanningProcessor#scanForExceptionMappers", "started": "12:24:37.682", "dependents": [528, 514, 406, 421], "id": 381, "thread": "build-59"}, {"duration": 8, "stepId": "io.quarkus.netty.deployment.NettyProcessor#cleanupMacDNSInLog", "started": "12:24:37.012", "dependents": [383, 226], "id": 163, "thread": "build-50"}, {"duration": 8, "stepId": "io.quarkus.hibernate.orm.deployment.dev.HibernateOrmDevUIProcessor#createJsonRPCService", "started": "12:24:36.936", "dependents": [365, 289], "id": 27, "thread": "build-23"}, {"duration": 7, "stepId": "io.quarkus.vertx.deployment.EventConsumerMethodsProcessor#eventConsumerMethods", "started": "12:24:37.028", "dependents": [327], "id": 206, "thread": "build-71"}, {"duration": 7, "stepId": "io.quarkus.rest.client.reactive.deployment.devconsole.RestClientReactiveDevUIProcessor#beans", "started": "12:24:36.980", "dependents": [406, 421], "id": 99, "thread": "build-49"}, {"duration": 7, "stepId": "io.quarkus.stork.deployment.SmallRyeStorkProcessor#unremoveableBeans", "started": "12:24:37.021", "dependents": [461, 469], "id": 193, "thread": "build-51"}, {"duration": 7, "stepId": "io.quarkus.jackson.deployment.JacksonProcessor#unremovable", "started": "12:24:37.585", "dependents": [406, 461, 421, 469], "id": 332, "thread": "build-26"}, {"duration": 7, "stepId": "io.quarkus.arc.deployment.StartupBuildSteps#unremovableBeans", "started": "12:24:37.014", "dependents": [461, 469], "id": 173, "thread": "build-68"}, {"duration": 7, "stepId": "io.quarkus.arc.deployment.SyntheticBeansProcessor#initStatic", "started": "12:24:38.145", "dependents": [532, 458], "id": 449, "thread": "build-31"}, {"duration": 7, "stepId": "io.quarkus.arc.deployment.ReflectiveBeanClassesProcessor#implicitReflectiveBeanClasses", "started": "12:24:38.078", "dependents": [481], "id": 429, "thread": "build-29"}, {"duration": 7, "stepId": "io.quarkus.rest.client.reactive.deployment.RestClientReactiveProcessor#announceFeature", "started": "12:24:36.923", "dependents": [532], "id": 10, "thread": "build-3"}, {"duration": 7, "stepId": "io.quarkus.smallrye.reactivemessaging.rabbitmq.deployment.devui.RabbitDevUIProcessor#registerJsonRpcBackend", "started": "12:24:36.996", "dependents": [365, 289], "id": 139, "thread": "build-27"}, {"duration": 7, "stepId": "io.quarkus.vertx.deployment.EventBusCodecProcessor#registerCodecs", "started": "12:24:37.787", "dependents": [528, 443], "id": 413, "thread": "build-31"}, {"duration": 7, "stepId": "io.quarkus.arc.deployment.WrongAnnotationUsageProcessor#detect", "started": "12:24:38.078", "dependents": [481], "id": 430, "thread": "build-59"}, {"duration": 7, "stepId": "io.quarkus.redis.deployment.client.DevServicesRedisProcessor#startRedisContainers", "started": "12:24:37.701", "dependents": [400, 404], "id": 387, "thread": "build-37"}, {"duration": 7, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveScanningProcessor#scanForParamConverters_59e3169e3a646b7fcf3083416f558434b73816c5", "started": "12:24:37.625", "dependents": [376], "id": 369, "thread": "build-23"}, {"duration": 7, "stepId": "io.quarkus.devservices.deployment.DevServicesProcessor#config", "started": "12:24:37.743", "dependents": [405, 502], "id": 404, "thread": "build-31"}, {"duration": 7, "stepId": "io.quarkus.arc.deployment.ArcProcessor#initializeContainer", "started": "12:24:38.768", "dependents": [532, 483], "id": 482, "thread": "build-135"}, {"duration": 7, "stepId": "io.quarkus.smallrye.reactivemessaging.rabbitmq.deployment.RabbitMQDevServicesProcessor#startRabbitMQDevService", "started": "12:24:37.701", "dependents": [400, 404], "id": 386, "thread": "build-31"}, {"duration": 7, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#filterNettyHostsFileParsingWarn", "started": "12:24:36.923", "dependents": [383, 226], "id": 6, "thread": "build-2"}, {"duration": 7, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#jpaEntitiesIndexer", "started": "12:24:37.585", "dependents": [531, 382], "id": 331, "thread": "build-36"}, {"duration": 6, "stepId": "io.quarkus.deployment.console.ConsoleProcessor#setupExceptionHandler", "started": "12:24:37.342", "dependents": [314], "id": 307, "thread": "build-34"}, {"duration": 6, "stepId": "io.quarkus.hibernate.orm.deployment.ResteasyReactiveServerIntegrationProcessor#unwrappedExceptions", "started": "12:24:36.950", "dependents": [381], "id": 44, "thread": "build-28"}, {"duration": 6, "stepId": "io.quarkus.arc.deployment.ConfigBuildStep#vetoMPConfigProperties", "started": "12:24:36.971", "dependents": [421], "id": 81, "thread": "build-43"}, {"duration": 6, "stepId": "io.quarkus.resteasy.reactive.common.deployment.ResteasyReactiveCommonProcessor#setupEndpoints", "started": "12:24:38.782", "dependents": [528, 508, 512, 509, 510], "id": 490, "thread": "build-64"}, {"duration": 6, "stepId": "io.quarkus.resteasy.reactive.common.deployment.ResteasyReactiveCommonProcessor#scanForParameterContainers", "started": "12:24:37.625", "dependents": [510, 500], "id": 368, "thread": "build-37"}, {"duration": 6, "stepId": "io.quarkus.mutiny.deployment.MutinyProcessor#runtimeInit", "started": "12:24:37.287", "dependents": [532], "id": 299, "thread": "build-34"}, {"duration": 6, "stepId": "io.quarkus.arc.deployment.SplitPackageProcessor#splitPackageDetection", "started": "12:24:37.570", "dependents": [481], "id": 315, "thread": "build-34"}, {"duration": 6, "stepId": "io.quarkus.deployment.logging.LoggingResourceProcessor#setupStackTraceFormatter", "started": "12:24:37.570", "dependents": [383], "id": 314, "thread": "build-12"}, {"duration": 6, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#overrideContextInternalInterfaceToAddSafeGuards", "started": "12:24:37.018", "dependents": [530], "id": 174, "thread": "build-3"}, {"duration": 6, "stepId": "io.quarkus.deployment.steps.DevServicesConfigBuildStep#deprecated", "started": "12:24:37.012", "dependents": [400], "id": 159, "thread": "build-62"}, {"duration": 6, "stepId": "io.quarkus.devui.deployment.BuildTimeContentProcessor#gatherMvnpmJars", "started": "12:24:36.981", "dependents": [511, 503], "id": 106, "thread": "build-10"}, {"duration": 6, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#additionalAsyncTypeMethodScanners", "started": "12:24:36.944", "dependents": [500], "id": 38, "thread": "build-25"}, {"duration": 6, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#providersFromClasspath", "started": "12:24:36.936", "dependents": [508, 512, 509, 510], "id": 23, "thread": "build-25"}, {"duration": 6, "stepId": "io.quarkus.agroal.deployment.AgroalProcessor#registerRowSetSupport", "started": "12:24:36.950", "dependents": [528], "id": 46, "thread": "build-33"}, {"duration": 6, "stepId": "io.quarkus.arc.deployment.ConfigBuildStep#registerConfigPropertiesBean", "started": "12:24:38.078", "dependents": [458], "id": 428, "thread": "build-66"}, {"duration": 5, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#integrateEagerSecurity", "started": "12:24:37.612", "dependents": [500], "id": 353, "thread": "build-14"}, {"duration": 5, "stepId": "io.quarkus.hibernate.orm.panache.common.deployment.PanacheJpaCommonResourceProcessor#lookupNamedQueries_5a86a91ed8ef1aa483288c8239df231983eeb766", "started": "12:24:37.701", "dependents": [388], "id": 385, "thread": "build-59"}, {"duration": 5, "stepId": "io.quarkus.arc.deployment.ArcProcessor#loggerProducer", "started": "12:24:37.031", "dependents": [406, 421], "id": 208, "thread": "build-75"}, {"duration": 5, "stepId": "io.quarkus.arc.deployment.ConfigBuildStep#validateConfigMappingsInjectionPoints", "started": "12:24:38.382", "dependents": [470, 472], "id": 469, "thread": "build-15"}, {"duration": 5, "stepId": "io.quarkus.smallrye.context.deployment.SmallRyeContextPropagationProcessor#registerBean", "started": "12:24:36.996", "dependents": [406, 421], "id": 138, "thread": "build-60"}, {"duration": 5, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmCdiProcessor#registerBeans", "started": "12:24:37.735", "dependents": [406, 461, 421, 469], "id": 399, "thread": "build-29"}, {"duration": 5, "stepId": "io.quarkus.resteasy.reactive.common.deployment.ResteasyReactiveCommonProcessor#buildResourceInterceptors", "started": "12:24:37.680", "dependents": [514, 406, 500, 421, 414], "id": 380, "thread": "build-19"}, {"duration": 5, "stepId": "io.quarkus.vertx.deployment.VertxProcessor#featureAndCapability", "started": "12:24:36.932", "dependents": [532, 177], "id": 21, "thread": "build-24"}, {"duration": 5, "stepId": "io.quarkus.arc.deployment.AutoAddScopeProcessor#annotationTransformer", "started": "12:24:37.790", "dependents": [461, 421, 469], "id": 415, "thread": "build-25"}, {"duration": 5, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveScanningProcessor#scanForInterceptors", "started": "12:24:37.625", "dependents": [380], "id": 367, "thread": "build-39"}, {"duration": 5, "stepId": "io.quarkus.devui.deployment.menu.ContinuousTestingProcessor#createContinuousTestingPages", "started": "12:24:36.931", "dependents": [480], "id": 19, "thread": "build-23"}, {"duration": 5, "stepId": "io.quarkus.deployment.steps.BlockingOperationControlBuildStep#blockingOP", "started": "12:24:37.222", "dependents": [532], "id": 280, "thread": "build-36"}, {"duration": 5, "stepId": "io.quarkus.vertx.http.deployment.StaticResourcesProcessor#runtimeInit", "started": "12:24:38.782", "dependents": [532, 522], "id": 489, "thread": "build-146"}, {"duration": 5, "stepId": "io.quarkus.deployment.execannotations.ExecutionModelAnnotationsProcessor#devuiJsonRpcServices", "started": "12:24:36.948", "dependents": [327], "id": 42, "thread": "build-15"}, {"duration": 5, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#registerHttpAuthMechanismAnnotations", "started": "12:24:36.971", "dependents": [349], "id": 77, "thread": "build-32"}, {"duration": 5, "stepId": "io.quarkus.deployment.steps.ConfigGenerationBuildStep#unknownConfigFiles", "started": "12:24:37.570", "dependents": [532], "id": 313, "thread": "build-38"}, {"duration": 5, "stepId": "io.quarkus.hibernate.orm.panache.deployment.PanacheHibernateResourceProcessor#ensureBeanLookupAvailable", "started": "12:24:36.925", "dependents": [461, 469], "id": 8, "thread": "build-16"}, {"duration": 5, "stepId": "io.quarkus.redis.deployment.client.RedisDatasourceProcessor#init", "started": "12:24:38.098", "dependents": [450, 532, 449, 454], "id": 444, "thread": "build-45"}, {"duration": 5, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#registerCustomExceptionMappers", "started": "12:24:36.931", "dependents": [379], "id": 18, "thread": "build-19"}, {"duration": 4, "stepId": "io.quarkus.smallrye.reactivemessaging.deployment.devui.ReactiveMessagingDevUIProcessor#beans", "started": "12:24:36.924", "dependents": [406, 421], "id": 1, "thread": "build-14"}, {"duration": 4, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveCDIProcessor#additionalBeans", "started": "12:24:37.635", "dependents": [528, 406, 421], "id": 374, "thread": "build-25"}, {"duration": 4, "stepId": "io.quarkus.scheduler.deployment.SchedulerMethodsProcessor#schedulerMethods", "started": "12:24:36.931", "dependents": [327], "id": 17, "thread": "build-3"}, {"duration": 4, "stepId": "io.quarkus.arc.deployment.AutoProducerMethodsProcessor#annotationTransformer", "started": "12:24:37.787", "dependents": [421], "id": 411, "thread": "build-45"}, {"duration": 4, "stepId": "io.quarkus.arc.deployment.LifecycleEventsBuildStep#startupEvent", "started": "12:24:39.911", "dependents": [532, 527], "id": 526, "thread": "build-237"}, {"duration": 4, "stepId": "io.quarkus.devui.deployment.ide.IdeProcessor#createJsonRPCService", "started": "12:24:37.223", "dependents": [286], "id": 279, "thread": "build-11"}, {"duration": 4, "stepId": "io.quarkus.hibernate.orm.deployment.dev.HibernateOrmDevServicesProcessor#devServicesAutoGenerateByDefault", "started": "12:24:37.735", "dependents": [400], "id": 398, "thread": "build-31"}, {"duration": 4, "stepId": "io.quarkus.config.yaml.deployment.ConfigYamlProcessor#yamlConfig", "started": "12:24:36.957", "dependents": [472], "id": 64, "thread": "build-16"}, {"duration": 4, "stepId": "io.quarkus.panache.common.deployment.PanacheHibernateCommonResourceProcessor#replaceFieldAccesses", "started": "12:24:38.996", "dependents": [530], "id": 496, "thread": "build-47"}, {"duration": 4, "stepId": "io.quarkus.netty.deployment.NettyProcessor#registerEventLoopBeans", "started": "12:24:37.340", "dependents": [450, 532, 449, 454], "id": 306, "thread": "build-39"}, {"duration": 4, "stepId": "io.quarkus.redis.deployment.client.RedisClientProcessor#feature", "started": "12:24:36.923", "dependents": [532], "id": 2, "thread": "build-4"}, {"duration": 4, "stepId": "io.quarkus.devui.deployment.menu.ExtensionsProcessor#createExtensionsPages", "started": "12:24:38.691", "dependents": [480], "id": 478, "thread": "build-245"}, {"duration": 4, "stepId": "io.quarkus.smallrye.reactivemessaging.rabbitmq.deployment.devui.RabbitDevUIProcessor#beans", "started": "12:24:37.019", "dependents": [406, 421], "id": 171, "thread": "build-69"}, {"duration": 4, "stepId": "io.quarkus.arc.deployment.ConfigBuildStep#registerConfigClasses", "started": "12:24:38.389", "dependents": [532], "id": 470, "thread": "build-29"}, {"duration": 4, "stepId": "io.quarkus.datasource.deployment.devui.DevUIDatasourceProcessor#registerJsonRpcBackend", "started": "12:24:37.023", "dependents": [365, 289], "id": 184, "thread": "build-50"}, {"duration": 4, "stepId": "io.quarkus.arc.deployment.StartupBuildSteps#registerStartupObservers", "started": "12:24:38.214", "dependents": [461], "id": 460, "thread": "build-31"}, {"duration": 4, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveCDIProcessor#subResourcesAsBeans", "started": "12:24:37.620", "dependents": [406, 461, 421, 469], "id": 363, "thread": "build-68"}, {"duration": 4, "stepId": "io.quarkus.arc.deployment.devui.ArcDevUIProcessor#pages", "started": "12:24:38.458", "dependents": [476, 477], "id": 475, "thread": "build-31"}, {"duration": 4, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmCdiProcessor#validatePersistenceUnitExtensions", "started": "12:24:38.383", "dependents": [481], "id": 468, "thread": "build-29"}, {"duration": 4, "stepId": "io.quarkus.deployment.steps.ConfigGenerationBuildStep#runtimeOverrideConfig", "started": "12:24:37.023", "dependents": [472], "id": 185, "thread": "build-71"}, {"duration": 4, "stepId": "io.quarkus.arc.deployment.staticmethods.InterceptedStaticMethodsProcessor#processInterceptedStaticMethods", "started": "12:24:38.092", "dependents": [528, 530, 496, 497, 495], "id": 441, "thread": "build-23"}, {"duration": 4, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#openSocket", "started": "12:24:39.915", "dependents": [528, 532], "id": 527, "thread": "build-150"}, {"duration": 3, "stepId": "io.quarkus.resteasy.reactive.jackson.deployment.processor.ResteasyReactiveJacksonProcessor#handleJsonAnnotations", "started": "12:24:39.207", "dependents": [528, 532, 507], "id": 506, "thread": "build-63"}, {"duration": 3, "stepId": "io.quarkus.netty.deployment.NettyProcessor#registerQualifiers", "started": "12:24:36.992", "dependents": [406, 421], "id": 125, "thread": "build-41"}, {"duration": 3, "stepId": "io.quarkus.arc.deployment.ShutdownBuildSteps#registerShutdownObservers", "started": "12:24:38.214", "dependents": [461], "id": 459, "thread": "build-23"}, {"duration": 3, "stepId": "io.quarkus.redis.deployment.client.RedisClientProcessor#makeHostsProviderAndOptionsCustomizerUnremovable", "started": "12:24:36.957", "dependents": [461, 469], "id": 61, "thread": "build-33"}, {"duration": 3, "stepId": "io.quarkus.narayana.jta.deployment.NarayanaJtaProcessor#transactionContext", "started": "12:24:37.854", "dependents": [424], "id": 422, "thread": "build-45"}, {"duration": 3, "stepId": "io.quarkus.smallrye.reactivemessaging.deployment.WiringProcessor#autoConfigureConnectorForOrphansAndProduceManagedChannels", "started": "12:24:38.095", "dependents": [472, 481, 451, 448], "id": 442, "thread": "build-46"}, {"duration": 3, "stepId": "io.quarkus.deployment.ForkJoinPoolProcessor#setProperty", "started": "12:24:36.923", "dependents": [532], "id": 4, "thread": "build-7"}, {"duration": 3, "stepId": "io.quarkus.devui.deployment.menu.ContinuousTestingProcessor#continuousTestingState", "started": "12:24:38.782", "dependents": [532], "id": 488, "thread": "build-219"}, {"duration": 3, "stepId": "io.quarkus.stork.deployment.SmallRyeStorkProcessor#initializeStork", "started": "12:24:38.207", "dependents": [532], "id": 457, "thread": "build-31"}, {"duration": 3, "stepId": "io.quarkus.arc.deployment.ArcProcessor#notifyBeanContainerListeners", "started": "12:24:38.776", "dependents": [532, 484], "id": 483, "thread": "build-66"}, {"duration": 3, "stepId": "io.quarkus.jdbc.postgresql.deployment.PostgreSQLJDBCReflections#build", "started": "12:24:37.002", "dependents": [528], "id": 141, "thread": "build-43"}, {"duration": 3, "stepId": "io.quarkus.smallrye.reactivemessaging.deployment.devui.ReactiveMessagingDevUIProcessor#collectInjectionInfo", "started": "12:24:38.078", "dependents": [532], "id": 427, "thread": "build-11"}, {"duration": 3, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#startPersistenceUnits", "started": "12:24:38.782", "dependents": [532, 523, 526], "id": 487, "thread": "build-66"}, {"duration": 3, "stepId": "io.quarkus.resteasy.reactive.common.deployment.ResteasyReactiveCommonProcessor#resourceIndex", "started": "12:24:37.585", "dependents": [406, 505, 354], "id": 330, "thread": "build-31"}, {"duration": 3, "stepId": "io.quarkus.smallrye.context.deployment.SmallRyeContextPropagationProcessor#createSynthBeansForConfiguredInjectionPoints", "started": "12:24:38.078", "dependents": [450, 532, 449, 454], "id": 426, "thread": "build-68"}, {"duration": 3, "stepId": "io.quarkus.arc.deployment.ArcProcessor#exposeCustomScopeNames", "started": "12:24:36.993", "dependents": [415, 360, 409, 406, 143, 152, 421, 229, 411, 430], "id": 127, "thread": "build-43"}, {"duration": 3, "stepId": "io.quarkus.deployment.dev.ConfigureDisableInstrumentationBuildStep#configure", "started": "12:24:36.990", "dependents": [523, 526], "id": 123, "thread": "build-27"}, {"duration": 3, "stepId": "io.quarkus.agroal.deployment.AgroalProcessor#generateDataSourceBeans", "started": "12:24:37.720", "dependents": [450, 487, 532, 449, 456, 393, 454, 394, 401], "id": 392, "thread": "build-66"}, {"duration": 3, "stepId": "io.quarkus.resteasy.reactive.jackson.deployment.processor.ResteasyReactiveJacksonProcessor#customExceptionMappers", "started": "12:24:36.924", "dependents": [379], "id": 3, "thread": "build-13"}, {"duration": 3, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#build_d182d2fe7ae008890806ec353e99fa052582ee2d", "started": "12:24:37.735", "dependents": [532, 497], "id": 397, "thread": "build-46"}, {"duration": 3, "stepId": "io.quarkus.devui.deployment.BuildTimeContentProcessor#mapPageBuildTimeData", "started": "12:24:38.463", "dependents": [499], "id": 476, "thread": "build-25"}, {"duration": 3, "stepId": "io.quarkus.narayana.jta.deployment.NarayanaJtaProcessor#registerScope", "started": "12:24:36.989", "dependents": [127], "id": 122, "thread": "build-49"}, {"duration": 3, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmAlwaysEnabledProcessor#featureBuildItem", "started": "12:24:36.927", "dependents": [532], "id": 9, "thread": "build-19"}, {"duration": 3, "stepId": "io.quarkus.arc.deployment.AutoInjectFieldProcessor#annotationTransformer", "started": "12:24:37.790", "dependents": [421], "id": 412, "thread": "build-40"}, {"duration": 3, "stepId": "io.quarkus.resteasy.reactive.common.deployment.ResteasyReactiveCommonProcessor#scanForIOInterceptors", "started": "12:24:37.625", "dependents": [380], "id": 366, "thread": "build-68"}, {"duration": 2, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveDevModeProcessor#openCommand", "started": "12:24:39.054", "dependents": [502], "id": 501, "thread": "build-226"}, {"duration": 2, "stepId": "io.quarkus.credentials.CredentialsProcessor#unremoveable", "started": "12:24:37.028", "dependents": [461, 469], "id": 202, "thread": "build-65"}, {"duration": 2, "stepId": "io.quarkus.arc.deployment.init.InitializationTaskProcessor#startApplicationInitializer", "started": "12:24:38.207", "dependents": [532], "id": 455, "thread": "build-45"}, {"duration": 2, "stepId": "io.quarkus.arc.deployment.AutoInjectFieldProcessor#autoInjectQualifiers", "started": "12:24:37.787", "dependents": [415, 412], "id": 410, "thread": "build-59"}, {"duration": 2, "stepId": "io.quarkus.deployment.steps.DevServicesConfigBuildStep#setup", "started": "12:24:37.740", "dependents": [423, 472, 452, 453, 523, 526, 404, 402], "id": 400, "thread": "build-46"}, {"duration": 2, "stepId": "io.quarkus.scheduler.deployment.devui.SchedulerDevUIProcessor#page", "started": "12:24:38.092", "dependents": [476, 477], "id": 438, "thread": "build-61"}, {"duration": 2, "stepId": "io.quarkus.devui.deployment.BuildTimeContentProcessor#loadAllBuildTimeTemplates", "started": "12:24:39.102", "dependents": [511], "id": 504, "thread": "build-63"}, {"duration": 2, "stepId": "io.quarkus.netty.deployment.NettyProcessor#cleanupUnsafeLog", "started": "12:24:36.988", "dependents": [383, 226], "id": 114, "thread": "build-54"}, {"duration": 2, "stepId": "io.quarkus.hibernate.orm.panache.common.deployment.PanacheJpaCommonResourceProcessor#buildNamedQueryMap", "started": "12:24:37.705", "dependents": [532], "id": 388, "thread": "build-19"}, {"duration": 2, "stepId": "io.quarkus.arc.deployment.ArcProcessor#unremovableAsyncObserverExceptionHandlers", "started": "12:24:36.957", "dependents": [461, 469], "id": 57, "thread": "build-10"}, {"duration": 2, "stepId": "io.quarkus.rest.client.reactive.deployment.RestClientReactiveProcessor#unremovableBeans", "started": "12:24:36.989", "dependents": [461, 469], "id": 119, "thread": "build-18"}, {"duration": 2, "stepId": "io.quarkus.hibernate.orm.panache.deployment.PanacheHibernateResourceProcessor#validate", "started": "12:24:38.382", "dependents": [481], "id": 466, "thread": "build-45"}, {"duration": 2, "stepId": "io.quarkus.narayana.jta.deployment.NarayanaJtaProcessor#startRecoveryService", "started": "12:24:38.207", "dependents": [532], "id": 456, "thread": "build-23"}, {"duration": 2, "stepId": "io.quarkus.devservices.postgresql.deployment.PostgresqlDevServicesProcessor#psqlCommand", "started": "12:24:37.743", "dependents": [502], "id": 402, "thread": "build-40"}, {"duration": 2, "stepId": "io.quarkus.smallrye.reactivemessaging.rabbitmq.deployment.devui.RabbitDevUIProcessor#createCard", "started": "12:24:37.022", "dependents": [476, 477], "id": 179, "thread": "build-58"}, {"duration": 2, "stepId": "io.quarkus.hibernate.orm.panache.deployment.PanacheHibernateResourceProcessor#recordEntityToPersistenceUnit", "started": "12:24:39.011", "dependents": [532], "id": 498, "thread": "build-47"}, {"duration": 2, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmCdiProcessor#convertJpaResourceAnnotationsToQualifier", "started": "12:24:37.735", "dependents": [421], "id": 396, "thread": "build-40"}, {"duration": 2, "stepId": "io.quarkus.smallrye.openapi.deployment.SmallRyeOpenApiProcessor#runtimeOnly", "started": "12:24:36.987", "dependents": [472], "id": 115, "thread": "build-27"}, {"duration": 2, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#produceLoggingCategories", "started": "12:24:36.978", "dependents": [94], "id": 89, "thread": "build-43"}, {"duration": 2, "stepId": "io.quarkus.deployment.steps.ShutdownListenerBuildStep#setupShutdown", "started": "12:24:39.911", "dependents": [532], "id": 525, "thread": "build-150"}, {"duration": 2, "stepId": "io.quarkus.arc.deployment.ObserverValidationProcessor#validateApplicationObserver", "started": "12:24:38.383", "dependents": [481], "id": 467, "thread": "build-66"}, {"duration": 2, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#additionalReflection", "started": "12:24:39.212", "dependents": [528], "id": 509, "thread": "build-61"}, {"duration": 2, "stepId": "io.quarkus.deployment.logging.LoggingResourceProcessor#setUpDefaultLogCleanupFilters", "started": "12:24:37.055", "dependents": [472], "id": 226, "thread": "build-66"}, {"duration": 2, "stepId": "io.quarkus.resteasy.reactive.server.deployment.devui.ResteasyReactiveDevUIProcessor#createJsonRPCService", "started": "12:24:36.947", "dependents": [365, 289], "id": 37, "thread": "build-11"}, {"duration": 2, "stepId": "io.quarkus.arc.deployment.ArcProcessor#signalBeanContainerReady", "started": "12:24:38.780", "dependents": [532, 487, 514, 512, 510, 485, 522, 490, 489, 526, 500, 486, 491, 488], "id": 484, "thread": "build-135"}, {"duration": 1, "stepId": "io.quarkus.arc.deployment.BuildTimeEnabledProcessor#findEnablementStereotypes", "started": "12:24:37.584", "dependents": [341, 338, 340, 335], "id": 328, "thread": "build-38"}, {"duration": 1, "stepId": "io.quarkus.redis.deployment.client.RedisClientProcessor#registerRedisClientName", "started": "12:24:36.929", "dependents": [406, 421], "id": 5, "thread": "build-20"}, {"duration": 1, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#convertRoutes", "started": "12:24:39.652", "dependents": [521, 520], "id": 519, "thread": "build-236"}, {"duration": 1, "stepId": "io.quarkus.deployment.steps.ProfileBuildStep#defaultProfile", "started": "12:24:36.959", "dependents": [472], "id": 59, "thread": "build-12"}, {"duration": 1, "stepId": "io.quarkus.devui.deployment.BuildTimeContentProcessor#mapDeploymentMethods", "started": "12:24:37.240", "dependents": [365, 491], "id": 286, "thread": "build-14"}, {"duration": 1, "stepId": "io.quarkus.devui.deployment.menu.EndpointsProcessor#createEndpointsPage", "started": "12:24:36.989", "dependents": [480], "id": 117, "thread": "build-29"}, {"duration": 1, "stepId": "io.quarkus.devui.deployment.BuildTimeContentProcessor#createKnownInternalImportMap", "started": "12:24:36.989", "dependents": [503], "id": 118, "thread": "build-41"}, {"duration": 1, "stepId": "io.quarkus.resteasy.reactive.jackson.deployment.processor.ResteasyReactiveJacksonProcessor#additionalProviders", "started": "12:24:39.209", "dependents": [508, 512, 509, 510], "id": 507, "thread": "build-64"}, {"duration": 1, "stepId": "io.quarkus.rest.client.reactive.deployment.devconsole.RestClientReactiveDevUIProcessor#createJsonRPCServiceForCache", "started": "12:24:36.947", "dependents": [365, 289], "id": 36, "thread": "build-17"}, {"duration": 1, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#config", "started": "12:24:36.945", "dependents": [472], "id": 32, "thread": "build-24"}, {"duration": 1, "stepId": "io.quarkus.arc.deployment.LookupConditionsProcessor#suppressConditionsGenerators", "started": "12:24:37.787", "dependents": [421], "id": 408, "thread": "build-40"}, {"duration": 1, "stepId": "io.quarkus.arc.deployment.staticmethods.InterceptedStaticMethodsProcessor#callInitializer", "started": "12:24:38.782", "dependents": [532], "id": 485, "thread": "build-225"}, {"duration": 1, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#httpRoot", "started": "12:24:36.987", "dependents": [511, 501, 522, 519, 524, 529, 243], "id": 107, "thread": "build-52"}, {"duration": 1, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#registerSafeDuplicatedContextInterceptor", "started": "12:24:36.942", "dependents": [406, 421], "id": 24, "thread": "build-24"}, {"duration": 1, "stepId": "io.quarkus.arc.deployment.ConfigBuildStep#registerCustomConfigBeanTypes", "started": "12:24:38.078", "dependents": [528, 450, 449, 454], "id": 425, "thread": "build-19"}, {"duration": 1, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#doNotRemoveVertxOptionsCustomizers", "started": "12:24:36.996", "dependents": [461, 469], "id": 129, "thread": "build-32"}, {"duration": 1, "stepId": "io.quarkus.smallrye.reactivemessaging.deployment.SmallRyeReactiveMessagingProcessor#transformBeanScope", "started": "12:24:37.787", "dependents": [421], "id": 409, "thread": "build-25"}, {"duration": 1, "stepId": "io.quarkus.arc.deployment.ConfigBuildStep#validateConfigPropertiesInjectionPoints", "started": "12:24:38.382", "dependents": [470], "id": 465, "thread": "build-39"}, {"duration": 1, "stepId": "io.quarkus.arc.deployment.HotDeploymentConfigBuildStep#configFile", "started": "12:24:36.929", "dependents": [447], "id": 7, "thread": "build-21"}, {"duration": 1, "stepId": "io.quarkus.arc.deployment.ConfigBuildStep#validateConfigValues", "started": "12:24:38.782", "dependents": [528, 532], "id": 486, "thread": "build-231"}, {"duration": 1, "stepId": "io.quarkus.deployment.steps.ApplicationInfoBuildStep#create", "started": "12:24:36.981", "dependents": [532], "id": 93, "thread": "build-43"}, {"duration": 1, "stepId": "io.quarkus.redis.deployment.client.RedisDatasourceProcessor#makeCodecsUnremovable", "started": "12:24:37.585", "dependents": [406, 421], "id": 329, "thread": "build-53"}, {"duration": 1, "stepId": "io.quarkus.deployment.logging.LoggingResourceProcessor#setUpDefaultLevels", "started": "12:24:36.981", "dependents": [472, 383], "id": 94, "thread": "build-35"}, {"duration": 0, "stepId": "io.quarkus.deployment.JniProcessor#setupJni", "started": "12:24:36.989", "dependents": [265], "id": 108, "thread": "build-55"}, {"duration": 0, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#produceEagerSecurityInterceptorStorage", "started": "12:24:37.612", "dependents": [450, 532, 449, 454], "id": 352, "thread": "build-11"}, {"duration": 0, "stepId": "io.quarkus.deployment.console.ConsoleProcessor#installCliCommands", "started": "12:24:39.057", "dependents": [523, 526], "id": 502, "thread": "build-63"}, {"duration": 0, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#cleanupVertxWarnings", "started": "12:24:37.021", "dependents": [383, 226], "id": 166, "thread": "build-7"}, {"duration": 0, "stepId": "io.quarkus.scheduler.deployment.SchedulerProcessor#unremoveableSkipPredicates", "started": "12:24:36.996", "dependents": [461, 469], "id": 126, "thread": "build-35"}, {"duration": 0, "stepId": "io.quarkus.deployment.steps.AdditionalClassLoaderResourcesBuildStep#appendAdditionalClassloaderResources", "started": "12:24:36.999", "dependents": [316], "id": 134, "thread": "build-56"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#candidatesForFieldAccess", "started": "12:24:37.701", "dependents": [389], "id": 384, "thread": "build-19"}, {"duration": 0, "stepId": "io.quarkus.datasource.deployment.devui.DevUIDatasourceProcessor#create", "started": "12:24:36.987", "dependents": [476, 477], "id": 104, "thread": "build-29"}, {"duration": 0, "stepId": "io.quarkus.deployment.index.ApplicationArchiveBuildStep#addConfiguredIndexedDependencies", "started": "12:24:36.976", "dependents": [312], "id": 78, "thread": "build-27"}, {"duration": 0, "stepId": "io.quarkus.rest.client.reactive.deployment.RestClientReactiveProcessor#handleSseEventFilter", "started": "12:24:37.787", "dependents": [528], "id": 407, "thread": "build-66"}, {"duration": 0, "stepId": "io.quarkus.arc.deployment.ArcProcessor#quarkusApplication", "started": "12:24:37.585", "dependents": [406, 421], "id": 325, "thread": "build-68"}, {"duration": 0, "stepId": "io.quarkus.deployment.execannotations.ExecutionModelAnnotationsProcessor#check", "started": "12:24:37.585", "dependents": [], "id": 327, "thread": "build-34"}, {"duration": 0, "stepId": "io.quarkus.rest.client.reactive.deployment.RestClientReactiveProcessor#activateSslNativeSupport", "started": "12:24:36.979", "dependents": [265], "id": 87, "thread": "build-35"}, {"duration": 0, "stepId": "io.quarkus.arc.deployment.ArcProcessor#marker", "started": "12:24:36.989", "dependents": [312], "id": 110, "thread": "build-29"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#unremovableBeans", "started": "12:24:37.620", "dependents": [461, 469], "id": 357, "thread": "build-14"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.panache.deployment.PanacheHibernateResourceProcessor#produceModel", "started": "12:24:36.950", "dependents": [531, 495, 331], "id": 40, "thread": "build-11"}, {"duration": 0, "stepId": "io.quarkus.deployment.logging.LoggingResourceProcessor#setProperty", "started": "12:24:37.028", "dependents": [532], "id": 194, "thread": "build-52"}, {"duration": 0, "stepId": "io.quarkus.rest.client.reactive.deployment.RestClientReactiveProcessor#registerProviderBeans", "started": "12:24:37.585", "dependents": [406, 421], "id": 322, "thread": "build-16"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveCDIProcessor#perClassExceptionMapperSupport", "started": "12:24:37.620", "dependents": [421], "id": 356, "thread": "build-11"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#registerSecurityInterceptors", "started": "12:24:37.027", "dependents": [406, 421], "id": 186, "thread": "build-27"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#multitenancy", "started": "12:24:37.735", "dependents": [450, 532, 449, 461, 469, 454], "id": 395, "thread": "build-37"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.jackson.deployment.processor.ResteasyReactiveJacksonProcessor#jacksonRegistered", "started": "12:24:37.028", "dependents": [233], "id": 196, "thread": "build-27"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#contributeQuarkusConfigToJpaModel", "started": "12:24:36.970", "dependents": [382], "id": 72, "thread": "build-35"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#addAllWriteableMarker", "started": "12:24:39.212", "dependents": [530], "id": 508, "thread": "build-63"}, {"duration": 0, "stepId": "io.quarkus.deployment.logging.LoggingWithPanacheProcessor#process", "started": "12:24:37.585", "dependents": [530], "id": 326, "thread": "build-44"}, {"duration": 0, "stepId": "io.quarkus.deployment.console.ConsoleProcessor#missingDevUIMessageHandler", "started": "12:24:37.342", "dependents": [523, 526], "id": 305, "thread": "build-46"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.server.deployment.devui.ResteasyReactiveDevUIProcessor#createPages", "started": "12:24:36.961", "dependents": [476, 477], "id": 63, "thread": "build-12"}, {"duration": 0, "stepId": "io.quarkus.arc.deployment.ConfigBuildStep#validateConfigInjectionPoints", "started": "12:24:38.383", "dependents": [486], "id": 464, "thread": "build-68"}, {"duration": 0, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#preventLoggerContention", "started": "12:24:36.959", "dependents": [94], "id": 55, "thread": "build-37"}, {"duration": 0, "stepId": "io.quarkus.config.yaml.deployment.ConfigYamlProcessor#watchYamlConfig", "started": "12:24:37.006", "dependents": [447], "id": 144, "thread": "build-27"}, {"duration": 0, "stepId": "io.quarkus.scheduler.deployment.SchedulerProcessor#produceCoroutineScope", "started": "12:24:36.933", "dependents": [406, 421], "id": 14, "thread": "build-25"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.jackson.deployment.processor.ResteasyReactiveJacksonProcessor#initializeRolesAllowedConfigExp", "started": "12:24:38.382", "dependents": [532], "id": 463, "thread": "build-46"}, {"duration": 0, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#initMtlsClientAuth", "started": "12:24:36.981", "dependents": [406, 421], "id": 91, "thread": "build-10"}, {"duration": 0, "stepId": "io.quarkus.narayana.jta.deployment.NarayanaJtaProcessor#logCleanupFilters", "started": "12:24:36.936", "dependents": [383, 226], "id": 20, "thread": "build-3"}, {"duration": 0, "stepId": "io.quarkus.arc.deployment.ArcProcessor#feature", "started": "12:24:36.942", "dependents": [532], "id": 22, "thread": "build-3"}, {"duration": 0, "stepId": "io.quarkus.deployment.recording.AnnotationProxyBuildStep#build", "started": "12:24:37.372", "dependents": [443, 446], "id": 311, "thread": "build-12"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#enrollBeanValidationTypeSafeActivatorForReflection", "started": "12:24:37.027", "dependents": [528], "id": 188, "thread": "build-67"}, {"duration": 0, "stepId": "io.quarkus.arc.deployment.ExecutorServiceProcessor#executorServiceBean", "started": "12:24:37.287", "dependents": [450, 449, 454], "id": 297, "thread": "build-53"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#buildSetup", "started": "12:24:37.023", "dependents": [532], "id": 172, "thread": "build-18"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#includeArchivesHostingEntityPackagesInIndex", "started": "12:24:36.981", "dependents": [312], "id": 92, "thread": "build-50"}, {"duration": 0, "stepId": "io.quarkus.smallrye.openapi.deployment.SmallRyeOpenApiProcessor#logCleanup", "started": "12:24:36.931", "dependents": [383, 226], "id": 11, "thread": "build-14"}, {"duration": 0, "stepId": "io.quarkus.deployment.ConstructorPropertiesProcessor#build", "started": "12:24:37.585", "dependents": [528], "id": 324, "thread": "build-13"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.panache.deployment.PanacheHibernateResourceProcessor#featureBuildItem", "started": "12:24:37.008", "dependents": [532], "id": 145, "thread": "build-27"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveCDIProcessor#pathInterfaceImpls", "started": "12:24:37.620", "dependents": [406, 421], "id": 355, "thread": "build-22"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#configureHandlers", "started": "12:24:39.638", "dependents": [532], "id": 517, "thread": "build-236"}, {"duration": 0, "stepId": "io.quarkus.devui.deployment.menu.BuildMetricsProcessor#createBuildMetricsPages", "started": "12:24:37.025", "dependents": [480], "id": 181, "thread": "build-72"}, {"duration": 0, "stepId": "io.quarkus.devui.deployment.menu.ConfigurationProcessor#createConfigurationPages", "started": "12:24:38.169", "dependents": [480], "id": 452, "thread": "build-45"}, {"duration": 0, "stepId": "io.quarkus.deployment.ExtensionLoader#booleanSupplierFactory", "started": "12:24:36.931", "dependents": [151], "id": 13, "thread": "build-13"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateLogFilterBuildStep#setupLogFilters", "started": "12:24:36.975", "dependents": [383, 226], "id": 76, "thread": "build-45"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.jackson.deployment.processor.ResteasyReactiveJacksonProcessor#feature", "started": "12:24:37.021", "dependents": [532], "id": 167, "thread": "build-50"}, {"duration": 0, "stepId": "io.quarkus.config.yaml.deployment.ConfigYamlProcessor#feature", "started": "12:24:37.028", "dependents": [532], "id": 195, "thread": "build-4"}, {"duration": 0, "stepId": "io.quarkus.smallrye.reactivemessaging.deployment.SmallRyeReactiveMessagingProcessor#feature", "started": "12:24:36.935", "dependents": [532], "id": 16, "thread": "build-25"}, {"duration": 0, "stepId": "io.quarkus.rest.client.reactive.deployment.RestClientReactiveProcessor#registerProvidersInstances", "started": "12:24:37.584", "dependents": [377], "id": 318, "thread": "build-34"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateUserTypeProcessor#build", "started": "12:24:37.585", "dependents": [528], "id": 319, "thread": "build-31"}, {"duration": 0, "stepId": "io.quarkus.jdbc.postgresql.deployment.JDBCPostgreSQLProcessor#feature", "started": "12:24:36.947", "dependents": [532], "id": 33, "thread": "build-23"}, {"duration": 0, "stepId": "io.quarkus.deployment.steps.ConfigGenerationBuildStep#suppressNonRuntimeConfigChanged", "started": "12:24:36.931", "dependents": [281], "id": 12, "thread": "build-20"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.common.deployment.ResteasyReactiveCommonProcessor#searchForProviders", "started": "12:24:37.025", "dependents": [312], "id": 180, "thread": "build-67"}, {"duration": 0, "stepId": "io.quarkus.rest.client.reactive.jackson.deployment.RestClientReactiveJacksonProcessor#feature", "started": "12:24:36.957", "dependents": [532], "id": 49, "thread": "build-29"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#handleClassLevelExceptionMappers", "started": "12:24:37.620", "dependents": [528, 500], "id": 358, "thread": "build-23"}, {"duration": 0, "stepId": "io.quarkus.smallrye.reactivemessaging.rabbitmq.deployment.SmallRyeReactiveMessagingRabbitMQProcessor#feature", "started": "12:24:36.956", "dependents": [532], "id": 43, "thread": "build-30"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#defineTypeOfImpliedPU", "started": "12:24:37.723", "dependents": [396, 394, 401], "id": 393, "thread": "build-40"}, {"duration": 0, "stepId": "io.quarkus.deployment.steps.MainClassBuildStep#applicationReflection", "started": "12:24:36.962", "dependents": [528], "id": 62, "thread": "build-39"}, {"duration": 0, "stepId": "io.quarkus.rest.client.reactive.deployment.devconsole.RestClientReactiveDevUIProcessor#create", "started": "12:24:36.999", "dependents": [476, 477], "id": 136, "thread": "build-42"}, {"duration": 0, "stepId": "io.quarkus.netty.deployment.NettyProcessor#limitArenaSize", "started": "12:24:37.013", "dependents": [532], "id": 156, "thread": "build-67"}, {"duration": 0, "stepId": "io.quarkus.arc.deployment.ArcProcessor#validateAsyncObserverExceptionHandlers", "started": "12:24:38.383", "dependents": [481], "id": 462, "thread": "build-11"}, {"duration": 0, "stepId": "io.quarkus.smallrye.reactivemessaging.deployment.SmallRyeReactiveMessagingProcessor#producesCoroutineConfiguration", "started": "12:24:37.029", "dependents": [198], "id": 197, "thread": "build-73"}, {"duration": 0, "stepId": "io.quarkus.arc.deployment.TestsAsBeansProcessor#testClassBeans", "started": "12:24:36.999", "dependents": [406, 421], "id": 135, "thread": "build-18"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#setMinimalNettyMaxOrderSize", "started": "12:24:37.008", "dependents": [183, 156], "id": 146, "thread": "build-43"}, {"duration": 0, "stepId": "io.quarkus.rest.client.reactive.deployment.RestClientReactiveProcessor#registerHeaderFactoryBeans", "started": "12:24:37.585", "dependents": [406, 421], "id": 317, "thread": "build-59"}, {"duration": 0, "stepId": "io.quarkus.deployment.steps.PreloadClassesBuildStep#registerPreInitClasses", "started": "12:24:36.950", "dependents": [], "id": 39, "thread": "build-17"}, {"duration": 0, "stepId": "io.quarkus.stork.deployment.SmallRyeStorkProcessor#checkThatTheKubernetesExtensionIsUsedWhenKubernetesServiceDiscoveryInOnTheClasspath", "started": "12:24:37.027", "dependents": [457], "id": 189, "thread": "build-58"}, {"duration": 0, "stepId": "io.quarkus.devui.deployment.DevUIProcessor#createAllRoutes", "started": "12:24:38.824", "dependents": [511], "id": 493, "thread": "build-64"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#contributePersistenceXmlToJpaModel", "started": "12:24:37.275", "dependents": [382], "id": 294, "thread": "build-53"}, {"duration": 0, "stepId": "io.quarkus.deployment.steps.BannerProcessor#watchBannerChanges", "started": "12:24:36.960", "dependents": [447], "id": 58, "thread": "build-10"}, {"duration": 0, "stepId": "io.quarkus.smallrye.openapi.deployment.SmallRyeOpenApiProcessor#contributeClassesToIndex", "started": "12:24:36.987", "dependents": [316], "id": 102, "thread": "build-13"}, {"duration": 0, "stepId": "io.quarkus.deployment.dev.testing.TestTracingProcessor#handle", "started": "12:24:37.031", "dependents": [383, 226], "id": 200, "thread": "build-52"}, {"duration": 0, "stepId": "io.quarkus.arc.deployment.BuildTimeEnabledProcessor#conditionTransformer", "started": "12:24:37.599", "dependents": [421], "id": 342, "thread": "build-67"}, {"duration": 0, "stepId": "io.quarkus.deployment.steps.CurateOutcomeBuildStep#curateOutcome", "started": "12:24:36.978", "dependents": [217, 492, 476, 241, 477, 286, 530, 343, 310, 365, 390, 499, 312, 480, 177, 160, 479, 121, 151, 257, 106], "id": 84, "thread": "build-29"}, {"duration": 0, "stepId": "io.quarkus.devui.deployment.build.BuildMetricsDevUIProcessor#createJsonRPCService", "started": "12:24:37.008", "dependents": [365, 289], "id": 148, "thread": "build-65"}, {"duration": 0, "stepId": "io.quarkus.rest.client.reactive.deployment.RestClientReactiveProcessor#registerCompressionInterceptors", "started": "12:24:37.008", "dependents": [528], "id": 149, "thread": "build-35"}, {"duration": 0, "stepId": "io.quarkus.deployment.pkg.steps.NativeImageBuildStep#ignoreBuildPropertyChanges", "started": "12:24:36.996", "dependents": [281], "id": 128, "thread": "build-18"}, {"duration": 0, "stepId": "io.quarkus.redis.deployment.client.RedisClientProcessor#activateSslNativeSupport", "started": "12:24:36.947", "dependents": [265], "id": 34, "thread": "build-26"}, {"duration": 0, "stepId": "io.quarkus.arc.deployment.LoggingBeanSupportProcessor#discoveredComponents", "started": "12:24:36.987", "dependents": [406, 421, 229], "id": 103, "thread": "build-49"}, {"duration": 0, "stepId": "io.quarkus.agroal.deployment.AgroalProcessor#agroal", "started": "12:24:37.006", "dependents": [532], "id": 142, "thread": "build-43"}, {"duration": 0, "stepId": "io.quarkus.smallrye.reactivemessaging.deployment.SmallRyeReactiveMessagingProcessor#produceCoroutineScope", "started": "12:24:37.029", "dependents": [406, 421], "id": 198, "thread": "build-27"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveCDIProcessor#unremovableContextMethodParams", "started": "12:24:37.620", "dependents": [461, 469], "id": 359, "thread": "build-15"}, {"duration": 0, "stepId": "io.quarkus.smallrye.reactivemessaging.deployment.SmallRyeReactiveMessagingProcessor#disableObservation", "started": "12:24:37.031", "dependents": [472], "id": 201, "thread": "build-27"}, {"duration": 0, "stepId": "io.quarkus.deployment.logging.LoggingResourceProcessor#setupLogFilters", "started": "12:24:36.983", "dependents": [383, 226], "id": 95, "thread": "build-29"}, {"duration": 0, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#notFoundRoutes", "started": "12:24:39.653", "dependents": [524], "id": 520, "thread": "build-25"}, {"duration": 0, "stepId": "io.quarkus.deployment.SslProcessor#setupNativeSsl", "started": "12:24:36.970", "dependents": [265, 392, 85, 285], "id": 73, "thread": "build-5"}, {"duration": 0, "stepId": "io.quarkus.devui.deployment.menu.DevServicesProcessor#createDevServicesPages", "started": "12:24:37.749", "dependents": [480], "id": 405, "thread": "build-19"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#checkTransactionsSupport", "started": "12:24:37.025", "dependents": [481], "id": 178, "thread": "build-68"}, {"duration": 0, "stepId": "io.quarkus.rest.client.reactive.deployment.RestClientReactiveProcessor#registerQueryParamStyleForConfig", "started": "12:24:36.947", "dependents": [295], "id": 35, "thread": "build-15"}, {"duration": 0, "stepId": "io.quarkus.jackson.deployment.JacksonProcessor#autoRegisterModules", "started": "12:24:37.584", "dependents": [361], "id": 320, "thread": "build-12"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.panache.deployment.PanacheHibernateResourceProcessor#collectEntityClasses", "started": "12:24:37.585", "dependents": [497], "id": 323, "thread": "build-24"}, {"duration": 0, "stepId": "io.quarkus.smallrye.reactivemessaging.deployment.devui.ReactiveMessagingDevUIProcessor#create", "started": "12:24:36.983", "dependents": [476, 477], "id": 96, "thread": "build-52"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#addPersistenceUnitAnnotationToIndex", "started": "12:24:36.957", "dependents": [316], "id": 50, "thread": "build-28"}, {"duration": 0, "stepId": "io.quarkus.smallrye.reactivemessaging.deployment.WiringProcessor#detectOrphanChannels", "started": "12:24:38.094", "dependents": [442], "id": 440, "thread": "build-61"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ObservabilityProcessor#methodScanner", "started": "12:24:37.027", "dependents": [500], "id": 192, "thread": "build-74"}, {"duration": 0, "stepId": "io.quarkus.agroal.deployment.AgroalProcessor#adaptOpenTelemetryJdbcInstrumentationForNative", "started": "12:24:37.027", "dependents": [530], "id": 187, "thread": "build-71"}, {"duration": 0, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#collectInterceptedMethods", "started": "12:24:37.612", "dependents": [352, 353], "id": 350, "thread": "build-22"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#warnOfSchemaProblems", "started": "12:24:39.911", "dependents": [532], "id": 523, "thread": "build-225"}, {"duration": 0, "stepId": "io.quarkus.deployment.steps.ReflectionDiagnosticProcessor#writeReflectionData", "started": "12:24:39.920", "dependents": [], "id": 528, "thread": "build-237"}, {"duration": 0, "stepId": "io.quarkus.caffeine.deployment.CaffeineProcessor#cacheLoaders", "started": "12:24:37.585", "dependents": [528], "id": 321, "thread": "build-21"}], "started": "2025-06-02T12:24:36.917", "items": [{"count": 1710, "class": "io.quarkus.deployment.builditem.GeneratedClassBuildItem"}, {"count": 1342, "class": "io.quarkus.deployment.builditem.ConfigDescriptionBuildItem"}, {"count": 977, "class": "io.quarkus.deployment.builditem.nativeimage.ReflectiveClassBuildItem"}, {"count": 668, "class": "io.quarkus.deployment.builditem.nativeimage.ReflectiveMethodBuildItem"}, {"count": 422, "class": "io.quarkus.deployment.builditem.BytecodeTransformerBuildItem"}, {"count": 417, "class": "io.quarkus.deployment.builditem.nativeimage.ReflectiveHierarchyBuildItem"}, {"count": 70, "class": "io.quarkus.hibernate.orm.panache.common.deployment.PanacheNamedQueryEntityClassBuildStep"}, {"count": 69, "class": "io.quarkus.arc.deployment.AdditionalBeanBuildItem"}, {"count": 67, "class": "io.quarkus.deployment.builditem.MainBytecodeRecorderBuildItem"}, {"count": 66, "class": "io.quarkus.hibernate.orm.panache.deployment.EntityToPersistenceUnitBuildItem"}, {"count": 58, "class": "io.quarkus.arc.deployment.SyntheticBeanBuildItem"}, {"count": 47, "class": "io.quarkus.deployment.builditem.StaticBytecodeRecorderBuildItem"}, {"count": 45, "class": "io.quarkus.vertx.http.deployment.RouteBuildItem"}, {"count": 38, "class": "io.quarkus.deployment.builditem.nativeimage.ReflectiveFieldBuildItem"}, {"count": 38, "class": "io.quarkus.deployment.builditem.nativeimage.RuntimeInitializedClassBuildItem"}, {"count": 35, "class": "io.quarkus.deployment.builditem.HotDeploymentWatchedFileBuildItem"}, {"count": 29, "class": "io.quarkus.arc.deployment.UnremovableBeanBuildItem"}, {"count": 24, "class": "io.quarkus.deployment.builditem.RunTimeConfigurationDefaultBuildItem"}, {"count": 20, "class": "io.quarkus.deployment.builditem.CapabilityBuildItem"}, {"count": 19, "class": "io.quarkus.deployment.builditem.FeatureBuildItem"}, {"count": 17, "class": "io.quarkus.deployment.logging.LogCleanupFilterBuildItem"}, {"count": 15, "class": "io.quarkus.deployment.builditem.AdditionalIndexedClassesBuildItem"}, {"count": 15, "class": "io.quarkus.deployment.builditem.ConfigClassBuildItem"}, {"count": 13, "class": "io.quarkus.vertx.http.deployment.webjar.WebJarBuildItem"}, {"count": 13, "class": "io.quarkus.arc.deployment.AnnotationsTransformerBuildItem"}, {"count": 12, "class": "io.quarkus.devui.deployment.DevUIWebJarBuildItem"}, {"count": 12, "class": "io.quarkus.devui.deployment.DevUIRoutesBuildItem"}, {"count": 12, "class": "io.quarkus.devui.spi.JsonRPCProvidersBuildItem"}, {"count": 11, "class": "io.quarkus.deployment.builditem.SuppressNonRuntimeConfigChangedWarningBuildItem"}, {"count": 10, "class": "io.quarkus.devui.spi.page.CardPageBuildItem"}, {"count": 10, "class": "io.quarkus.arc.deployment.GeneratedBeanBuildItem"}, {"count": 10, "class": "io.quarkus.deployment.builditem.nativeimage.RuntimeReinitializedClassBuildItem"}, {"count": 10, "class": "io.quarkus.scheduler.deployment.ScheduledBusinessMethodItem"}, {"count": 10, "class": "io.quarkus.resteasy.reactive.spi.MessageBodyWriterBuildItem"}, {"count": 8, "class": "io.quarkus.hibernate.orm.deployment.spi.DatabaseKindDialectBuildItem"}, {"count": 7, "class": "io.quarkus.deployment.builditem.ConsoleCommandBuildItem"}, {"count": 7, "class": "io.quarkus.resteasy.reactive.spi.MessageBodyReaderBuildItem"}, {"count": 7, "class": "io.quarkus.devui.deployment.InternalPageBuildItem"}, {"count": 7, "class": "io.quarkus.resteasy.reactive.spi.ExceptionMapperBuildItem"}, {"count": 7, "class": "io.quarkus.vertx.http.deployment.devmode.NotFoundPageDisplayableEndpointBuildItem"}, {"count": 6, "class": "io.quarkus.deployment.builditem.SystemPropertyBuildItem"}, {"count": 6, "class": "io.quarkus.arc.deployment.BeanDefiningAnnotationBuildItem"}, {"count": 6, "class": "io.quarkus.deployment.builditem.nativeimage.NativeImageSystemPropertyBuildItem"}, {"count": 6, "class": "io.quarkus.resteasy.reactive.server.spi.MethodScannerBuildItem"}, {"count": 6, "class": "io.quarkus.devui.deployment.BuildTimeConstBuildItem"}, {"count": 6, "class": "io.quarkus.arc.deployment.ConfigPropertyBuildItem"}, {"count": 5, "class": "io.quarkus.deployment.builditem.RunTimeConfigBuilderBuildItem"}, {"count": 5, "class": "io.quarkus.deployment.execannotations.ExecutionModelAnnotationsAllowedBuildItem"}, {"count": 5, "class": "io.quarkus.deployment.builditem.nativeimage.NativeImageResourceBuildItem"}, {"count": 4, "class": "io.quarkus.resteasy.reactive.spi.MessageBodyWriterOverrideBuildItem"}, {"count": 4, "class": "io.quarkus.deployment.builditem.ServiceStartBuildItem"}, {"count": 4, "class": "io.quarkus.arc.deployment.BeanRegistrationPhaseBuildItem$BeanConfiguratorBuildItem"}, {"count": 4, "class": "io.quarkus.arc.deployment.AutoAddScopeBuildItem"}, {"count": 4, "class": "io.quarkus.vertx.http.deployment.spi.RouteBuildItem"}, {"count": 4, "class": "io.quarkus.devui.spi.buildtime.BuildTimeActionBuildItem"}, {"count": 4, "class": "io.quarkus.resteasy.reactive.spi.MessageBodyReaderOverrideBuildItem"}, {"count": 4, "class": "io.quarkus.deployment.builditem.ConfigMappingBuildItem"}, {"count": 4, "class": "io.quarkus.deployment.builditem.AdditionalApplicationArchiveMarkerBuildItem"}, {"count": 3, "class": "io.quarkus.vertx.http.deployment.HttpAuthMechanismAnnotationBuildItem"}, {"count": 3, "class": "io.quarkus.deployment.builditem.StaticInitConfigBuilderBuildItem"}, {"count": 3, "class": "io.quarkus.jackson.spi.ClassPathJacksonModuleBuildItem"}, {"count": 3, "class": "io.quarkus.deployment.builditem.ExtensionSslNativeSupportBuildItem"}, {"count": 3, "class": "io.quarkus.vertx.http.deployment.FilterBuildItem"}, {"count": 3, "class": "io.quarkus.resteasy.reactive.server.spi.UnwrappedExceptionBuildItem"}, {"count": 3, "class": "io.quarkus.resteasy.reactive.spi.CustomExceptionMapperBuildItem"}, {"count": 3, "class": "io.quarkus.deployment.builditem.GeneratedResourceBuildItem"}, {"count": 3, "class": "io.quarkus.resteasy.reactive.spi.ContainerRequestFilterBuildItem"}, {"count": 2, "class": "io.quarkus.deployment.builditem.ShutdownListenerBuildItem"}, {"count": 2, "class": "io.quarkus.resteasy.reactive.common.deployment.ResourceInterceptorsContributorBuildItem"}, {"count": 2, "class": "io.quarkus.smallrye.reactivemessaging.deployment.items.ConnectorBuildItem"}, {"count": 2, "class": "io.quarkus.deployment.builditem.ObjectSubstitutionBuildItem"}, {"count": 2, "class": "io.quarkus.devui.spi.buildtime.QuteTemplateBuildItem"}, {"count": 2, "class": "io.quarkus.deployment.builditem.nativeimage.NativeImageConfigBuildItem"}, {"count": 2, "class": "io.quarkus.deployment.builditem.RecordableConstructorBuildItem"}, {"count": 2, "class": "io.quarkus.resteasy.reactive.spi.ContainerResponseFilterBuildItem"}, {"count": 2, "class": "io.quarkus.deployment.builditem.BytecodeRecorderObjectLoaderBuildItem"}, {"count": 2, "class": "io.quarkus.devui.spi.buildtime.StaticContentBuildItem"}, {"count": 2, "class": "io.quarkus.deployment.builditem.LogCategoryBuildItem"}, {"count": 2, "class": "io.quarkus.deployment.dev.testing.TestListenerBuildItem"}, {"count": 2, "class": "io.quarkus.devui.deployment.InternalImportMapBuildItem"}, {"count": 2, "class": "io.quarkus.arc.deployment.AutoInjectAnnotationBuildItem"}, {"count": 1, "class": "io.quarkus.devui.deployment.MvnpmBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.AnnotationProxyBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.BytecodeRecorderConstantDefinitionBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.console.ConsoleInstalledBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.SynthesisFinishedBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.nativeimage.NativeImageResourceBundleBuildItem"}, {"count": 1, "class": "io.quarkus.vertx.core.deployment.EventLoopCountBuildItem"}, {"count": 1, "class": "io.quarkus.vertx.core.deployment.CoreVertxBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.server.deployment.ContextResolversBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.DockerStatusBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.nativeimage.ReflectiveHierarchyIgnoreWarningBuildItem"}, {"count": 1, "class": "io.quarkus.vertx.deployment.LocalCodecSelectorTypesBuildItem"}, {"count": 1, "class": "io.quarkus.vertx.http.deployment.InitialRouterBuildItem"}, {"count": 1, "class": "io.quarkus.panache.common.deployment.HibernateMetamodelForFieldAccessBuildItem"}, {"count": 1, "class": "io.quarkus.smallrye.openapi.deployment.spi.OpenApiDocumentBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.dev.ExceptionNotificationBuildItem"}, {"count": 1, "class": "io.quarkus.swaggerui.deployment.SwaggerUiBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.pkg.builditem.CompiledJavaVersionBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.ValidationPhaseBuildItem"}, {"count": 1, "class": "io.quarkus.jaxrs.client.reactive.deployment.JaxrsClientReactiveEnricherBuildItem"}, {"count": 1, "class": "io.quarkus.netty.deployment.EventLoopSupplierBuildItem"}, {"count": 1, "class": "io.quarkus.smallrye.reactivemessaging.deployment.items.ConnectorManagedChannelBuildItem"}, {"count": 1, "class": "io.quarkus.smallrye.reactivemessaging.deployment.items.MediatorBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.LogFileFormatBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.BooleanSupplierFactoryBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.server.deployment.ParamConverterProvidersBuildItem"}, {"count": 1, "class": "io.quarkus.hibernate.orm.deployment.ImpliedBlockingPersistenceUnitTypeBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.server.spi.HandlerConfigurationProviderBuildItem"}, {"count": 1, "class": "io.quarkus.datasource.deployment.spi.DevServicesDatasourceProviderBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.DevServicesLauncherConfigResultBuildItem"}, {"count": 1, "class": "io.quarkus.smallrye.reactivemessaging.deployment.items.ChannelBuildItem"}, {"count": 1, "class": "io.quarkus.panache.common.deployment.PanacheEntityClassesBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.ThreadFactoryBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.ApplicationIndexBuildItem"}, {"count": 1, "class": "io.quarkus.panache.common.deployment.HibernateModelClassCandidatesForFieldAccessBuildItem"}, {"count": 1, "class": "io.quarkus.agroal.spi.JdbcDriverBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.logging.LoggingSetupBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.ArcContainerBuildItem"}, {"count": 1, "class": "io.quarkus.devui.deployment.JsonRPCRuntimeMethodsBuildItem"}, {"count": 1, "class": "io.quarkus.smallrye.context.deployment.spi.ThreadContextProviderBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.BeanRegistrationPhaseBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.ApplicationClassNameBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.StreamingLogHandlerBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.dev.DisableInstrumentationForIndexPredicateBuildItem"}, {"count": 1, "class": "io.quarkus.hibernate.orm.deployment.spi.AdditionalJpaModelBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.LogConsoleFormatBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.CurrentContextFactoryBuildItem"}, {"count": 1, "class": "io.quarkus.rest.client.reactive.deployment.AnnotationToRegisterIntoClientContextBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.common.deployment.ParameterContainersBuildItem"}, {"count": 1, "class": "io.quarkus.smallrye.openapi.deployment.spi.AddToOpenAPIDefinitionBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.ConfigurationBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.ApplicationClassPredicateBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.common.deployment.ApplicationResultBuildItem"}, {"count": 1, "class": "io.quarkus.agroal.deployment.AggregatedDataSourceBuildTimeConfigBuildItem"}, {"count": 1, "class": "io.quarkus.vertx.http.deployment.BodyHandlerBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.BuildExclusionsBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.LogCategoryMinLevelDefaultsBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.IOThreadDetectorBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.SslNativeConfigBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.CustomScopeBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.common.deployment.ServerDefaultProducesHandlerBuildItem"}, {"count": 1, "class": "io.quarkus.agroal.spi.JdbcDataSourceBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.ide.IdeRunningProcessBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.BeanContainerListenerBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.TransformedClassesBuildItem"}, {"count": 1, "class": "io.quarkus.netty.deployment.EventLoopGroupBuildItem"}, {"count": 1, "class": "io.quarkus.hibernate.orm.deployment.JpaModelIndexBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.devui.ArcBeanInfoBuildItem"}, {"count": 1, "class": "io.quarkus.datasource.deployment.spi.DevServicesDatasourceConfigurationHandlerBuildItem"}, {"count": 1, "class": "io.quarkus.jaxrs.client.reactive.deployment.RestClientDefaultProducesBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.BeanDiscoveryFinishedBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.RunTimeConfigurationProxyBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.ConfigurationTypeBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.common.deployment.ResourceInterceptorsBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.BuildCompatibleExtensionsBuildItem"}, {"count": 1, "class": "io.quarkus.devui.deployment.ThemeVarsBuildItem"}, {"count": 1, "class": "io.quarkus.datasource.deployment.spi.DefaultDataSourceDbKindBuildItem"}, {"count": 1, "class": "io.quarkus.hibernate.orm.deployment.JpaModelPersistenceUnitMappingBuildItem"}, {"count": 1, "class": "io.quarkus.devui.spi.page.FooterPageBuildItem"}, {"count": 1, "class": "io.quarkus.smallrye.context.deployment.ContextPropagationInitializedBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.server.deployment.ExceptionMappersBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.InterceptorResolverBuildItem"}, {"count": 1, "class": "io.quarkus.panache.common.deployment.HibernateEnhancersRegisteredBuildItem"}, {"count": 1, "class": "io.quarkus.datasource.deployment.spi.DevServicesDatasourceResultBuildItem"}, {"count": 1, "class": "io.quarkus.smallrye.reactivemessaging.deployment.SmallRyeReactiveMessagingProcessor$CoroutineConfigurationBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.BeanArchiveIndexBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.spi.ParamConverterBuildItem"}, {"count": 1, "class": "io.quarkus.jackson.spi.JacksonModuleBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.ConsoleFormatterBannerBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.SuppressConditionGeneratorBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.BuildTimeEnabledStereotypesBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.ApplicationArchivesBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.ContextHandlerBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.TransformedAnnotationsBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveResourceMethodEntriesBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.GeneratedFileSystemResourceHandledBuildItem"}, {"count": 1, "class": "io.quarkus.hibernate.orm.deployment.PersistenceProviderSetUpBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.pkg.builditem.OutputTargetBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.PreBeanContainerBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.InjectionPointTransformerBuildItem"}, {"count": 1, "class": "io.quarkus.vertx.http.deployment.webjar.WebJarResultsBuildItem"}, {"count": 1, "class": "io.quarkus.hibernate.orm.deployment.JpaModelPersistenceUnitContributionBuildItem"}, {"count": 1, "class": "io.quarkus.netty.deployment.MinNettyAllocatorMaxOrderBuildItem"}, {"count": 1, "class": "io.quarkus.smallrye.openapi.deployment.OpenApiFilteredIndexViewBuildItem"}, {"count": 1, "class": "io.quarkus.vertx.http.deployment.NonApplicationRootPathBuildItem"}, {"count": 1, "class": "io.quarkus.vertx.http.deployment.VertxWebRouterBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.CombinedIndexBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.jackson.deployment.processor.ResteasyReactiveJacksonProviderDefinedBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.Capabilities"}, {"count": 1, "class": "io.quarkus.devui.deployment.ExtensionsBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.ExecutorBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.server.deployment.SetupEndpointsResultBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveDeploymentInfoBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.ObserverRegistrationPhaseBuildItem"}, {"count": 1, "class": "io.quarkus.jaxrs.client.reactive.deployment.RestClientDefaultConsumesBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.common.deployment.ResourceScanningResultBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.server.deployment.ServerSerialisersBuildItem"}, {"count": 1, "class": "io.quarkus.hibernate.orm.deployment.JpaModelBuildItem"}, {"count": 1, "class": "io.quarkus.vertx.deployment.VertxBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveDeploymentBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.BeanContainerBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.ide.EffectiveIdeBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.common.deployment.JaxRsResourceIndexBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.LogSyslogFormatBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.pkg.builditem.CurateOutcomeBuildItem"}, {"count": 1, "class": "io.quarkus.vertx.http.deployment.HttpRootPathBuildItem"}, {"count": 1, "class": "io.quarkus.devui.deployment.DeploymentMethodBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.steps.CapabilityAggregationStep$CapabilitiesConfiguredInDescriptorsBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.ApplicationStartBuildItem"}, {"count": 1, "class": "io.quarkus.redis.deployment.client.RequestedRedisClientBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.ContextRegistrationPhaseBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.CustomScopeAnnotationsBuildItem"}, {"count": 1, "class": "io.quarkus.hibernate.orm.deployment.PersistenceUnitDescriptorBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.ContextRegistrationPhaseBuildItem$ContextConfiguratorBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.server.deployment.BuiltInReaderOverrideBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.CompletedApplicationClassPredicateBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.ApplicationInfoBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.ide.IdeFileBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.MainClassBuildItem"}], "itemsCount": 6655, "buildTarget": "backend-admin-1.0-SNAPSHOT"}