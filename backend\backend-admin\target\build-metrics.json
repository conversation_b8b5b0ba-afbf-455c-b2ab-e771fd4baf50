{"duration": 3605, "records": [{"duration": 2019, "stepId": "io.quarkus.smallrye.openapi.deployment.SmallRyeOpenApiProcessor#build", "started": "12:12:54.964", "dependents": [532], "id": 530, "thread": "build-34"}, {"duration": 955, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#enhancerDomainObjects", "started": "12:12:55.217", "dependents": [529, 497, 496], "id": 495, "thread": "build-6"}, {"duration": 606, "stepId": "io.quarkus.deployment.steps.ClassTransformingBuildStep#handleClassTransformation", "started": "12:12:56.319", "dependents": [531], "id": 529, "thread": "build-6"}, {"duration": 583, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#pregenProxies", "started": "12:12:56.925", "dependents": [532], "id": 531, "thread": "build-80"}, {"duration": 373, "stepId": "io.quarkus.arc.deployment.ArcProcessor#generateResources", "started": "12:12:55.542", "dependents": [529, 528, 482], "id": 481, "thread": "build-48"}, {"duration": 292, "stepId": "io.quarkus.devui.deployment.BuildTimeContentProcessor#createBuildTimeConstJsTemplate", "started": "12:12:55.894", "dependents": [504, 503], "id": 499, "thread": "build-141"}, {"duration": 290, "stepId": "io.quarkus.devui.deployment.DevUIProcessor#getAllExtensions", "started": "12:12:55.563", "dependents": [478, 479, 493, 480, 492], "id": 477, "thread": "build-55"}, {"duration": 285, "stepId": "io.quarkus.deployment.logging.LoggingResourceProcessor#logConsoleCommand", "started": "12:12:54.166", "dependents": [502], "id": 296, "thread": "build-20"}, {"duration": 285, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#setupDeployment", "started": "12:12:56.354", "dependents": [515, 528, 517, 516, 532, 518, 521, 520, 523], "id": 514, "thread": "build-178"}, {"duration": 272, "stepId": "io.quarkus.deployment.steps.ApplicationIndexBuildStep#build", "started": "12:12:54.264", "dependents": [312, 421, 510, 435, 311, 500, 430], "id": 310, "thread": "build-66"}, {"duration": 268, "stepId": "io.quarkus.arc.deployment.ArcProcessor#buildCompatibleExtensions", "started": "12:12:54.148", "dependents": [421, 406], "id": 290, "thread": "build-6"}, {"duration": 258, "stepId": "io.quarkus.deployment.console.ConsoleProcessor#setupConsole", "started": "12:12:54.259", "dependents": [385, 308, 384, 314, 307, 383, 309], "id": 306, "thread": "build-33"}, {"duration": 254, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#setupEndpoints", "started": "12:12:55.933", "dependents": [529, 528, 532, 514, 505, 509, 512, 501], "id": 500, "thread": "build-123"}, {"duration": 238, "stepId": "io.quarkus.deployment.steps.MainClassBuildStep#build", "started": "12:12:57.508", "dependents": [], "id": 532, "thread": "build-34"}, {"duration": 234, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#parsePersistenceXmlDescriptors", "started": "12:12:54.259", "dependents": [303, 394], "id": 302, "thread": "build-47"}, {"duration": 234, "stepId": "io.quarkus.swaggerui.deployment.SwaggerUiProcessor#getSwaggerUiFinalDestination", "started": "12:12:54.889", "dependents": [492], "id": 423, "thread": "build-18"}, {"duration": 228, "stepId": "io.quarkus.devui.deployment.menu.ConfigurationProcessor#registerJsonRpcService", "started": "12:12:54.162", "dependents": [454, 532, 449, 281, 286, 364, 450], "id": 280, "thread": "build-2"}, {"duration": 227, "stepId": "io.quarkus.arc.deployment.ArcProcessor#registerBeans", "started": "12:12:54.974", "dependents": [432, 445, 435, 426, 449, 436, 431, 428, 430, 427, 450, 437, 454, 425, 433, 434, 440, 458, 439, 429], "id": 424, "thread": "build-64"}, {"duration": 214, "stepId": "io.quarkus.deployment.steps.ConfigGenerationBuildStep#generateConfigClass", "started": "12:12:54.259", "dependents": [], "id": 298, "thread": "build-71"}, {"duration": 212, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#addDefaultAuthFailureHandler", "started": "12:12:54.161", "dependents": [532, 518, 523], "id": 263, "thread": "build-18"}, {"duration": 204, "stepId": "io.quarkus.devui.deployment.DevUIProcessor#registerDevUiHandlers", "started": "12:12:56.248", "dependents": [532, 521, 520], "id": 513, "thread": "build-30"}, {"duration": 201, "stepId": "io.quarkus.rest.client.reactive.deployment.RestClientReactiveProcessor#setupAdditionalBeans", "started": "12:12:54.162", "dependents": [532, 421, 406], "id": 253, "thread": "build-22"}, {"duration": 200, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#ioThreadDetector", "started": "12:12:54.172", "dependents": [532, 276], "id": 268, "thread": "build-25"}, {"duration": 194, "stepId": "io.quarkus.vertx.http.deployment.console.ConsoleProcessor#setupConsole", "started": "12:12:54.274", "dependents": [524, 526], "id": 297, "thread": "build-56"}, {"duration": 193, "stepId": "io.quarkus.devui.deployment.logstream.LogStreamProcessor#handler", "started": "12:12:54.180", "dependents": [382, 532], "id": 271, "thread": "build-8"}, {"duration": 190, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#produceNamedHttpSecurityPolicies", "started": "12:12:54.172", "dependents": [454, 532, 449, 450], "id": 254, "thread": "build-14"}, {"duration": 187, "stepId": "io.quarkus.deployment.index.ApplicationArchiveBuildStep#build", "started": "12:12:54.536", "dependents": [313, 529, 522, 445, 316, 406, 348, 496, 314, 466, 315, 394], "id": 312, "thread": "build-9"}, {"duration": 186, "stepId": "io.quarkus.devui.deployment.build.BuildMetricsDevUIProcessor#create", "started": "12:12:54.172", "dependents": [532], "id": 245, "thread": "build-13"}, {"duration": 184, "stepId": "io.quarkus.mutiny.reactive.operators.deployment.MutinyReactiveStreamsOperatorsProcessor#classLoadingHack", "started": "12:12:54.174", "dependents": [532], "id": 250, "thread": "build-11"}, {"duration": 181, "stepId": "io.quarkus.deployment.steps.PreloadClassesBuildStep#preInit", "started": "12:12:54.172", "dependents": [532], "id": 241, "thread": "build-12"}, {"duration": 176, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#createVertxThreadFactory", "started": "12:12:54.197", "dependents": [282, 532], "id": 264, "thread": "build-48"}, {"duration": 171, "stepId": "io.quarkus.deployment.dev.io.NioThreadPoolDevModeProcessor#setupTCCL", "started": "12:12:54.187", "dependents": [532], "id": 243, "thread": "build-32"}, {"duration": 167, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#bodyHandler", "started": "12:12:54.322", "dependents": [532, 523], "id": 300, "thread": "build-24"}, {"duration": 164, "stepId": "io.quarkus.mutiny.deployment.MutinyProcessor#buildTimeInit", "started": "12:12:54.194", "dependents": [532], "id": 247, "thread": "build-3"}, {"duration": 161, "stepId": "io.quarkus.deployment.steps.BannerProcessor#recordBanner", "started": "12:12:54.321", "dependents": [382, 532], "id": 299, "thread": "build-55"}, {"duration": 154, "stepId": "io.quarkus.deployment.ide.IdeProcessor#detectRunningIdeProcesses", "started": "12:12:54.203", "dependents": [261], "id": 251, "thread": "build-57"}, {"duration": 153, "stepId": "io.quarkus.hibernate.orm.deployment.dev.HibernateOrmDevUIProcessor#additionalBeans", "started": "12:12:54.182", "dependents": [421, 406], "id": 239, "thread": "build-28"}, {"duration": 153, "stepId": "io.quarkus.deployment.console.ConsoleProcessor#helpCommand", "started": "12:12:54.182", "dependents": [502], "id": 234, "thread": "build-5"}, {"duration": 152, "stepId": "io.quarkus.arc.deployment.ArcProcessor#validate", "started": "12:12:55.330", "dependents": [465, 466, 474, 481, 529, 463, 473, 469, 462, 468, 471, 464, 467], "id": 461, "thread": "build-18"}, {"duration": 150, "stepId": "io.quarkus.virtual.threads.VirtualThreadsProcessor#setup", "started": "12:12:54.259", "dependents": [454, 532, 421, 406, 449, 450], "id": 287, "thread": "build-40"}, {"duration": 149, "stepId": "io.quarkus.netty.deployment.NettyProcessor#eagerlyInitClass", "started": "12:12:54.209", "dependents": [532], "id": 249, "thread": "build-34"}, {"duration": 146, "stepId": "io.quarkus.arc.deployment.ArcProcessor#quarkusMain", "started": "12:12:54.189", "dependents": [421, 406, 240], "id": 236, "thread": "build-10"}, {"duration": 142, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmCdiProcessor#registerAnnotations", "started": "12:12:54.193", "dependents": [421, 406, 240], "id": 237, "thread": "build-44"}, {"duration": 140, "stepId": "io.quarkus.deployment.console.ConsoleProcessor#quitCommand", "started": "12:12:54.195", "dependents": [502], "id": 235, "thread": "build-49"}, {"duration": 135, "stepId": "io.quarkus.narayana.jta.deployment.NarayanaJtaProcessor#build", "started": "12:12:54.737", "dependents": [528, 532, 421, 406, 456, 392], "id": 391, "thread": "build-63"}, {"duration": 129, "stepId": "io.quarkus.agroal.deployment.AgroalProcessor#build", "started": "12:12:54.292", "dependents": [295, 528, 421, 406, 392, 294, 293], "id": 291, "thread": "build-52"}, {"duration": 127, "stepId": "io.quarkus.devui.deployment.build.BuildMetricsDevUIProcessor#additionalBeans", "started": "12:12:54.208", "dependents": [421, 406], "id": 238, "thread": "build-46"}, {"duration": 121, "stepId": "io.quarkus.vertx.http.deployment.webjar.WebJarProcessor#processWebJarDevMode", "started": "12:12:55.853", "dependents": [494, 532, 493], "id": 492, "thread": "build-8"}, {"duration": 119, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#defineJpaEntities", "started": "12:12:54.746", "dependents": [387, 446, 528, 397, 388, 488, 531, 402, 495, 399, 394], "id": 386, "thread": "build-2"}, {"duration": 117, "stepId": "io.quarkus.deployment.steps.ConfigDescriptionBuildStep#createConfigDescriptions", "started": "12:12:54.264", "dependents": [453, 452], "id": 277, "thread": "build-61"}, {"duration": 117, "stepId": "io.quarkus.smallrye.openapi.deployment.SmallRyeOpenApiProcessor#handler", "started": "12:12:56.643", "dependents": [522, 532, 519], "id": 518, "thread": "build-178"}, {"duration": 116, "stepId": "io.quarkus.smallrye.context.deployment.SmallRyeContextPropagationProcessor#buildStatic", "started": "12:12:54.278", "dependents": [532], "id": 285, "thread": "build-69"}, {"duration": 114, "stepId": "io.quarkus.vertx.deployment.VertxProcessor#currentContextFactory", "started": "12:12:54.259", "dependents": [532, 482], "id": 269, "thread": "build-64"}, {"duration": 114, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#createVertxContextHandlers", "started": "12:12:54.259", "dependents": [282, 532, 284], "id": 265, "thread": "build-51"}, {"duration": 113, "stepId": "io.quarkus.deployment.logging.LoggingResourceProcessor#setupLoggingRuntimeInit", "started": "12:12:54.736", "dependents": [385, 528, 532, 384, 525, 383], "id": 382, "thread": "build-55"}, {"duration": 107, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveCDIProcessor#contextInjection", "started": "12:12:54.164", "dependents": [414, 412, 421, 406], "id": 195, "thread": "build-4"}, {"duration": 107, "stepId": "io.quarkus.deployment.steps.RuntimeConfigSetupBuildStep#setupRuntimeConfig", "started": "12:12:54.214", "dependents": [494, 516, 382, 397, 267, 299, 523, 305, 455, 274, 527, 273, 517, 532, 244, 521, 272, 315, 487, 242, 266, 282, 275, 279, 445, 456, 300, 525, 392, 288, 255, 463, 526, 444, 518, 391, 457, 301, 417], "id": 233, "thread": "build-39"}, {"duration": 106, "stepId": "io.quarkus.resteasy.reactive.jackson.deployment.processor.ResteasyReactiveJacksonProcessor#handleFieldSecurity", "started": "12:12:56.188", "dependents": [506], "id": 505, "thread": "build-141"}, {"duration": 105, "stepId": "io.quarkus.smallrye.reactivemessaging.rabbitmq.deployment.SmallRyeReactiveMessagingRabbitMQProcessor#dynamicCredentials", "started": "12:12:54.259", "dependents": [454, 532, 421, 406, 449, 472, 450], "id": 258, "thread": "build-27"}, {"duration": 104, "stepId": "io.quarkus.deployment.logging.LoggingResourceProcessor#registerMetrics", "started": "12:12:54.259", "dependents": [382, 532], "id": 256, "thread": "build-67"}, {"duration": 101, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#initFormAuth", "started": "12:12:54.259", "dependents": [532, 421, 406, 521, 520], "id": 252, "thread": "build-65"}, {"duration": 101, "stepId": "io.quarkus.devui.deployment.logstream.LogStreamProcessor#createJsonRPCService", "started": "12:12:54.179", "dependents": [281, 286, 364], "id": 211, "thread": "build-36"}, {"duration": 99, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#build_68c59e5d5fe4deeaa2b750dd2b2f234cee36c063", "started": "12:12:54.393", "dependents": [442, 449, 523, 305, 450, 527, 454, 524, 532, 526, 521, 487, 304], "id": 301, "thread": "build-9"}, {"duration": 98, "stepId": "io.quarkus.deployment.logging.LoggingResourceProcessor#setupLoggingStaticInit", "started": "12:12:54.259", "dependents": [532], "id": 246, "thread": "build-53"}, {"duration": 95, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#finalizeRouter", "started": "12:12:56.773", "dependents": [524, 526, 532, 525], "id": 523, "thread": "build-163"}, {"duration": 93, "stepId": "io.quarkus.smallrye.openapi.deployment.SmallRyeOpenApiProcessor#classLoaderHack", "started": "12:12:54.264", "dependents": [532], "id": 248, "thread": "build-70"}, {"duration": 91, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmCdiProcessor#generateJpaConfigBean", "started": "12:12:54.322", "dependents": [454, 532, 449, 450], "id": 288, "thread": "build-31"}, {"duration": 87, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#setupAuthenticationMechanisms", "started": "12:12:54.285", "dependents": [532, 421, 406, 420, 518, 523, 417], "id": 262, "thread": "build-4"}, {"duration": 87, "stepId": "io.quarkus.arc.deployment.ConfigStaticInitBuildSteps#transformConfigProducer", "started": "12:12:54.182", "dependents": [421], "id": 189, "thread": "build-38"}, {"duration": 86, "stepId": "io.quarkus.devui.deployment.menu.ContinuousTestingProcessor#createJsonRPCService", "started": "12:12:54.196", "dependents": [281, 286, 364], "id": 213, "thread": "build-16"}, {"duration": 86, "stepId": "io.quarkus.vertx.http.deployment.devmode.NotFoundProcessor#routeNotFound", "started": "12:12:56.773", "dependents": [532], "id": 522, "thread": "build-75"}, {"duration": 82, "stepId": "io.quarkus.jsonp.deployment.JsonpProcessor#build", "started": "12:12:54.174", "dependents": [528, 532], "id": 163, "thread": "build-29"}, {"duration": 80, "stepId": "io.quarkus.deployment.steps.ClassPathSystemPropBuildStep#set", "started": "12:12:54.284", "dependents": [532], "id": 257, "thread": "build-16"}, {"duration": 80, "stepId": "io.quarkus.vertx.http.deployment.ManagementInterfaceSecurityProcessor#setupAuthenticationMechanisms", "started": "12:12:54.285", "dependents": [532, 421, 406, 523], "id": 260, "thread": "build-36"}, {"duration": 80, "stepId": "io.quarkus.vertx.http.deployment.StaticResourcesProcessor#collectStaticResources", "started": "12:12:54.285", "dependents": [487], "id": 259, "thread": "build-35"}, {"duration": 78, "stepId": "io.quarkus.arc.deployment.LoggingBeanSupportProcessor#discoveredComponents", "started": "12:12:54.172", "dependents": [421, 406, 240], "id": 154, "thread": "build-30"}, {"duration": 76, "stepId": "io.quarkus.arc.deployment.devui.ArcDevModeApiProcessor#collectBeanInfo", "started": "12:12:55.482", "dependents": [475], "id": 474, "thread": "build-47"}, {"duration": 72, "stepId": "io.quarkus.deployment.steps.CompiledJavaVersionBuildStep#compiledJavaVersion", "started": "12:12:54.209", "dependents": [500], "id": 212, "thread": "build-7"}, {"duration": 69, "stepId": "io.quarkus.vertx.deployment.VertxProcessor#unremovableBeans", "started": "12:12:54.170", "dependents": [469, 461], "id": 149, "thread": "build-24"}, {"duration": 69, "stepId": "io.quarkus.deployment.steps.RegisterForReflectionBuildStep#build", "started": "12:12:54.737", "dependents": [528, 512], "id": 378, "thread": "build-26"}, {"duration": 67, "stepId": "io.quarkus.deployment.steps.ConfigGenerationBuildStep#checkForBuildTimeConfigChange", "started": "12:12:54.322", "dependents": [532], "id": 279, "thread": "build-37"}, {"duration": 66, "stepId": "io.quarkus.swaggerui.deployment.SwaggerUiProcessor#brandingFiles", "started": "12:12:54.193", "dependents": [446], "id": 170, "thread": "build-43"}, {"duration": 65, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveScanningProcessor#handleCustomAnnotatedMethods", "started": "12:12:54.767", "dependents": [421, 406, 381, 380], "id": 379, "thread": "build-18"}, {"duration": 64, "stepId": "io.quarkus.smallrye.reactivemessaging.deployment.WiringProcessor#generateDocumentationItem", "started": "12:12:55.221", "dependents": [453, 452], "id": 451, "thread": "build-47"}, {"duration": 63, "stepId": "io.quarkus.deployment.steps.ConfigGenerationBuildStep#watchConfigFiles", "started": "12:12:54.209", "dependents": [446], "id": 191, "thread": "build-60"}, {"duration": 61, "stepId": "io.quarkus.deployment.steps.ConfigGenerationBuildStep#buildTimeRunTimeConfig", "started": "12:12:54.259", "dependents": [528, 472], "id": 232, "thread": "build-63"}, {"duration": 61, "stepId": "io.quarkus.netty.deployment.NettyProcessor#setNettyMachineId", "started": "12:12:54.196", "dependents": [532], "id": 166, "thread": "build-47"}, {"duration": 61, "stepId": "io.quarkus.deployment.ExtensionLoader#config", "started": "12:12:54.197", "dependents": [443, 312, 382, 177, 530, 299, 529, 385, 517, 265, 225, 258, 242, 320, 399, 383, 363, 445, 291, 302, 420, 198, 349, 186, 474, 224, 524, 172, 472, 301, 252, 240, 193, 398, 435, 287, 260, 202, 305, 455, 482, 310, 309, 447, 411, 207, 365, 315, 487, 206, 182, 190, 510, 384, 525, 188, 199, 392, 288, 197, 361, 513, 209, 418, 377, 261, 526, 469, 232, 471, 221, 173, 412, 406, 174, 267, 430, 179, 298, 204, 274, 492, 481, 244, 521, 272, 269, 200, 278, 275, 279, 196, 514, 456, 348, 180, 473, 262, 183, 518, 448, 457, 194, 210, 293, 423, 467, 494, 432, 360, 516, 397, 523, 176, 527, 175, 256, 273, 532, 266, 313, 282, 475, 306, 300, 226, 246, 255, 334, 184, 394, 421, 444, 391, 248, 500, 417, 277], "id": 169, "thread": "build-50"}, {"duration": 60, "stepId": "io.quarkus.rest.client.reactive.deployment.RestClientReactiveProcessor#registerProvidersFromAnnotations", "started": "12:12:54.737", "dependents": [528, 406, 469, 461], "id": 377, "thread": "build-61"}, {"duration": 58, "stepId": "io.quarkus.resteasy.reactive.common.deployment.ResteasyReactiveCommonProcessor#deprioritizeLegacyProviders", "started": "12:12:54.199", "dependents": [511, 510], "id": 165, "thread": "build-21"}, {"duration": 58, "stepId": "io.quarkus.devui.deployment.BuildTimeContentProcessor#createIndexHtmlTemplate", "started": "12:12:56.187", "dependents": [504], "id": 503, "thread": "build-30"}, {"duration": 58, "stepId": "io.quarkus.scheduler.deployment.SchedulerProcessor#validateScheduledBusinessMethods", "started": "12:12:55.482", "dependents": [481], "id": 473, "thread": "build-56"}, {"duration": 56, "stepId": "io.quarkus.logging.json.deployment.LoggingJsonProcessor#setUpFileFormatter", "started": "12:12:54.322", "dependents": [382, 532], "id": 275, "thread": "build-9"}, {"duration": 55, "stepId": "io.quarkus.logging.json.deployment.LoggingJsonProcessor#setUpConsoleFormatter", "started": "12:12:54.321", "dependents": [382, 532], "id": 273, "thread": "build-26"}, {"duration": 55, "stepId": "io.quarkus.deployment.steps.ConfigGenerationBuildStep#setupConfigOverride", "started": "12:12:54.209", "dependents": [], "id": 185, "thread": "build-62"}, {"duration": 55, "stepId": "io.quarkus.rest.client.reactive.jackson.deployment.RestClientReactiveJacksonProcessor#additionalProviders_3f333413be4c0802e30f75e67ce4dd421dc2e40b", "started": "12:12:54.164", "dependents": [421, 406, 511, 510, 509, 508], "id": 133, "thread": "build-26"}, {"duration": 55, "stepId": "io.quarkus.logging.json.deployment.LoggingJsonProcessor#setUpSyslogFormatter", "started": "12:12:54.321", "dependents": [382, 532], "id": 274, "thread": "build-68"}, {"duration": 55, "stepId": "io.quarkus.scheduler.deployment.SchedulerProcessor#autoAddScope", "started": "12:12:54.202", "dependents": [414], "id": 168, "thread": "build-51"}, {"duration": 55, "stepId": "io.quarkus.deployment.steps.ReflectiveHierarchyStep#build", "started": "12:12:56.319", "dependents": [528], "id": 512, "thread": "build-8"}, {"duration": 54, "stepId": "io.quarkus.datasource.deployment.DataSourcesExcludedFromHealthChecksProcessor#produceBean", "started": "12:12:54.321", "dependents": [454, 532, 449, 450], "id": 272, "thread": "build-19"}, {"duration": 54, "stepId": "io.quarkus.hibernate.orm.panache.deployment.PanacheHibernateResourceProcessor#ensureBeanLookupAvailable", "started": "12:12:54.172", "dependents": [469, 461], "id": 144, "thread": "build-15"}, {"duration": 54, "stepId": "io.quarkus.arc.deployment.CommandLineArgumentsProcessor#commandLineArgs", "started": "12:12:54.209", "dependents": [454, 421, 406, 449, 450], "id": 181, "thread": "build-61"}, {"duration": 53, "stepId": "io.quarkus.arc.deployment.SyntheticBeansProcessor#initRuntime", "started": "12:12:55.265", "dependents": [524, 526, 532, 488, 456, 457, 455, 458], "id": 454, "thread": "build-2"}, {"duration": 52, "stepId": "io.quarkus.config.yaml.deployment.ConfigYamlProcessor#watchYamlConfig", "started": "12:12:54.202", "dependents": [446], "id": 161, "thread": "build-52"}, {"duration": 52, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#eventLoopCount", "started": "12:12:54.321", "dependents": [527, 532], "id": 267, "thread": "build-63"}, {"duration": 51, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#cors", "started": "12:12:54.321", "dependents": [532, 518, 523], "id": 266, "thread": "build-29"}, {"duration": 51, "stepId": "io.quarkus.deployment.dev.testing.TestTracingProcessor#testConsoleCommand", "started": "12:12:54.737", "dependents": [502], "id": 374, "thread": "build-34"}, {"duration": 51, "stepId": "io.quarkus.deployment.steps.ConfigGenerationBuildStep#runtimeOverrideConfig", "started": "12:12:54.201", "dependents": [472], "id": 157, "thread": "build-42"}, {"duration": 50, "stepId": "io.quarkus.redis.deployment.client.RedisClientProcessor#registerRedisClientName", "started": "12:12:54.202", "dependents": [421, 406], "id": 156, "thread": "build-41"}, {"duration": 49, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#recordableConstructor", "started": "12:12:54.174", "dependents": [532], "id": 136, "thread": "build-35"}, {"duration": 44, "stepId": "io.quarkus.vertx.deployment.VertxProcessor#autoAddScope", "started": "12:12:54.214", "dependents": [414], "id": 167, "thread": "build-27"}, {"duration": 44, "stepId": "io.quarkus.arc.deployment.ShutdownBuildSteps#addScope", "started": "12:12:54.214", "dependents": [414], "id": 171, "thread": "build-65"}, {"duration": 44, "stepId": "io.quarkus.smallrye.reactivemessaging.deployment.SmallRyeReactiveMessagingProcessor#build", "started": "12:12:55.221", "dependents": [454, 528, 532, 449, 472, 450], "id": 448, "thread": "build-63"}, {"duration": 43, "stepId": "io.quarkus.vertx.deployment.VertxJsonProcessor#registerJacksonSerDeser", "started": "12:12:54.214", "dependents": [362], "id": 164, "thread": "build-54"}, {"duration": 43, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveCDIProcessor#beanDefiningAnnotations", "started": "12:12:54.207", "dependents": [421, 406, 240], "id": 155, "thread": "build-56"}, {"duration": 42, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#securityExceptionMappers", "started": "12:12:54.166", "dependents": [381], "id": 107, "thread": "build-9"}, {"duration": 41, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#setupPersistenceProvider", "started": "12:12:54.322", "dependents": [532, 488, 278], "id": 255, "thread": "build-15"}, {"duration": 40, "stepId": "io.quarkus.scheduler.deployment.SchedulerProcessor#unremoveableSkipPredicates", "started": "12:12:54.187", "dependents": [469, 461], "id": 143, "thread": "build-37"}, {"duration": 40, "stepId": "io.quarkus.arc.deployment.StartupBuildSteps#addScope", "started": "12:12:54.215", "dependents": [414], "id": 159, "thread": "build-68"}, {"duration": 40, "stepId": "io.quarkus.deployment.DockerStatusProcessor#IsDockerWorking", "started": "12:12:54.184", "dependents": [385, 384, 383, 404], "id": 140, "thread": "build-17"}, {"duration": 40, "stepId": "io.quarkus.arc.deployment.ArcProcessor#initialize", "started": "12:12:54.932", "dependents": [424, 422, 474, 439], "id": 421, "thread": "build-64"}, {"duration": 39, "stepId": "io.quarkus.devui.deployment.DevUIProcessor#findAllJsonRPCMethods", "started": "12:12:54.736", "dependents": [499, 491], "id": 364, "thread": "build-47"}, {"duration": 39, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#filterMultipleVertxInstancesWarning", "started": "12:12:54.209", "dependents": [382, 160], "id": 152, "thread": "build-19"}, {"duration": 39, "stepId": "io.quarkus.rest.client.reactive.deployment.RestClientReactiveProcessor#setupRequestCollectingFilter", "started": "12:12:54.223", "dependents": [380], "id": 178, "thread": "build-69"}, {"duration": 39, "stepId": "io.quarkus.rest.client.reactive.deployment.RestClientReactiveProcessor#addMpClientEnricher", "started": "12:12:54.214", "dependents": [510], "id": 158, "thread": "build-23"}, {"duration": 38, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#serverSerializers", "started": "12:12:56.303", "dependents": [528, 532, 514], "id": 511, "thread": "build-123"}, {"duration": 37, "stepId": "io.quarkus.smallrye.reactivemessaging.deployment.SmallRyeReactiveMessagingProcessor#removalExclusions", "started": "12:12:54.219", "dependents": [469, 461], "id": 162, "thread": "build-64"}, {"duration": 37, "stepId": "io.quarkus.smallrye.reactivemessaging.deployment.SmallRyeReactiveMessagingProcessor#beans", "started": "12:12:54.175", "dependents": [421, 406], "id": 120, "thread": "build-33"}, {"duration": 36, "stepId": "io.quarkus.deployment.steps.ConfigGenerationBuildStep#releaseConfigOnShutdown", "started": "12:12:54.321", "dependents": [532], "id": 244, "thread": "build-42"}, {"duration": 35, "stepId": "io.quarkus.scheduler.deployment.SchedulerProcessor#build", "started": "12:12:55.216", "dependents": [454, 528, 532, 449, 450], "id": 447, "thread": "build-2"}, {"duration": 34, "stepId": "io.quarkus.devui.deployment.BuildTimeContentProcessor#createBuildTimeData", "started": "12:12:55.860", "dependents": [499, 503], "id": 480, "thread": "build-116"}, {"duration": 34, "stepId": "io.quarkus.jackson.deployment.JacksonProcessor#generateCustomizer", "started": "12:12:54.737", "dependents": [406], "id": 362, "thread": "build-29"}, {"duration": 34, "stepId": "io.quarkus.rest.client.reactive.deployment.RestClientReactiveProcessor#addRestClientBeans", "started": "12:12:54.737", "dependents": [532, 406], "id": 361, "thread": "build-36"}, {"duration": 34, "stepId": "io.quarkus.devui.deployment.build.BuildMetricsDevUIProcessor#createJsonRPCService", "started": "12:12:54.173", "dependents": [286, 364], "id": 105, "thread": "build-34"}, {"duration": 33, "stepId": "io.quarkus.deployment.steps.ConfigGenerationBuildStep#generateMappings", "started": "12:12:54.737", "dependents": [528, 469, 426, 429, 467], "id": 360, "thread": "build-6"}, {"duration": 33, "stepId": "io.quarkus.arc.deployment.BeanArchiveProcessor#build", "started": "12:12:54.892", "dependents": [414, 432, 445, 510, 407, 436, 408, 409, 411, 433, 410, 473, 421, 509, 490, 415, 500, 413, 439, 416], "id": 406, "thread": "build-61"}, {"duration": 33, "stepId": "io.quarkus.deployment.steps.ConfigGenerationBuildStep#generateBuilders", "started": "12:12:55.487", "dependents": [528], "id": 472, "thread": "build-48"}, {"duration": 32, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#setMtlsCertificateRoleProperties", "started": "12:12:54.321", "dependents": [532], "id": 242, "thread": "build-21"}, {"duration": 31, "stepId": "io.quarkus.deployment.steps.ProfileBuildStep#defaultProfile", "started": "12:12:54.193", "dependents": [472], "id": 139, "thread": "build-31"}, {"duration": 31, "stepId": "io.quarkus.arc.deployment.ArcProcessor#setupExecutor", "started": "12:12:54.393", "dependents": [532], "id": 292, "thread": "build-64"}, {"duration": 29, "stepId": "io.quarkus.resteasy.reactive.common.deployment.ResteasyReactiveCommonProcessor#scanResources", "started": "12:12:54.737", "dependents": [510, 358, 514, 375, 421, 355, 356, 490, 415, 506, 354, 500, 359, 379, 357], "id": 353, "thread": "build-51"}, {"duration": 27, "stepId": "io.quarkus.vertx.http.deployment.devmode.ArcDevProcessor#registerRoutes", "started": "12:12:55.482", "dependents": [481, 522, 532, 521, 520], "id": 471, "thread": "build-55"}, {"duration": 27, "stepId": "io.quarkiverse.caffeine.deployment.devui.CaffeineDevUIProcessor#createCard", "started": "12:12:54.162", "dependents": [476, 477], "id": 78, "thread": "build-23"}, {"duration": 27, "stepId": "io.quarkus.smallrye.reactivemessaging.deployment.SmallRyeReactiveMessagingProcessor#enableMetrics", "started": "12:12:54.259", "dependents": [421], "id": 224, "thread": "build-29"}, {"duration": 26, "stepId": "io.quarkus.hibernate.orm.deployment.GraalVMFeatures#registerJdbcArrayTypesForReflection", "started": "12:12:54.201", "dependents": [528], "id": 146, "thread": "build-55"}, {"duration": 26, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#registerAuthMechanismSelectionInterceptor", "started": "12:12:54.737", "dependents": [532, 435, 351, 352], "id": 349, "thread": "build-52"}, {"duration": 25, "stepId": "io.quarkus.resteasy.reactive.server.deployment.devui.ResteasyReactiveDevUIProcessor#createPages", "started": "12:12:54.148", "dependents": [476, 477], "id": 41, "thread": "build-8"}, {"duration": 25, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#initBasicAuth", "started": "12:12:54.259", "dependents": [421, 406, 420, 417], "id": 221, "thread": "build-24"}, {"duration": 24, "stepId": "io.quarkus.resteasy.reactive.common.deployment.ResteasyReactiveCommonProcessor#handleApplication", "started": "12:12:54.752", "dependents": [528, 511, 510, 514, 370, 367, 372, 369, 368, 381, 490, 500, 366, 371, 380], "id": 365, "thread": "build-48"}, {"duration": 24, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#vertxIntegration", "started": "12:12:54.219", "dependents": [511, 510, 509, 508], "id": 151, "thread": "build-40"}, {"duration": 23, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#generateCustomProducer", "started": "12:12:54.767", "dependents": [421, 406], "id": 375, "thread": "build-64"}, {"duration": 23, "stepId": "io.quarkus.stork.deployment.SmallRyeStorkProcessor#unremoveableBeans", "started": "12:12:54.149", "dependents": [469, 461], "id": 28, "thread": "build-10"}, {"duration": 23, "stepId": "io.quarkus.smallrye.openapi.deployment.SmallRyeOpenApiProcessor#addAutoFilters", "started": "12:12:54.940", "dependents": [530], "id": 420, "thread": "build-2"}, {"duration": 23, "stepId": "io.quarkus.datasource.deployment.devui.DevUIDatasourceProcessor#registerJsonRpcBackend", "started": "12:12:54.149", "dependents": [286, 364], "id": 36, "thread": "build-14"}, {"duration": 23, "stepId": "io.quarkus.arc.deployment.devui.ArcDevUIProcessor#createJsonRPCService", "started": "12:12:54.189", "dependents": [286, 364], "id": 115, "thread": "build-27"}, {"duration": 22, "stepId": "io.quarkus.smallrye.context.deployment.SmallRyeContextPropagationProcessor#build", "started": "12:12:54.393", "dependents": [454, 532, 449, 450], "id": 289, "thread": "build-2"}, {"duration": 22, "stepId": "io.quarkus.deployment.steps.MainClassBuildStep#mainClassBuildStep", "started": "12:12:54.737", "dependents": [529], "id": 348, "thread": "build-64"}, {"duration": 22, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#openSocket", "started": "12:12:56.873", "dependents": [528, 532], "id": 527, "thread": "build-102"}, {"duration": 20, "stepId": "io.quarkus.agroal.deployment.AgroalProcessor#generateDataSourceSupportBean", "started": "12:12:54.421", "dependents": [454, 532, 421, 406, 469, 449, 461, 450], "id": 295, "thread": "build-6"}, {"duration": 19, "stepId": "io.quarkus.deployment.pkg.steps.JarResultBuildStep#outputTarget", "started": "12:12:54.259", "dependents": [226, 227, 530], "id": 210, "thread": "build-59"}, {"duration": 19, "stepId": "io.quarkus.arc.deployment.ArcProcessor#registerContextPropagation", "started": "12:12:54.259", "dependents": [285], "id": 209, "thread": "build-19"}, {"duration": 19, "stepId": "io.quarkus.deployment.steps.ClassPathSystemPropBuildStep#produce", "started": "12:12:54.264", "dependents": [257], "id": 214, "thread": "build-35"}, {"duration": 19, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#overrideContextInternalInterfaceToAddSafeGuards", "started": "12:12:54.187", "dependents": [529], "id": 102, "thread": "build-40"}, {"duration": 19, "stepId": "io.quarkus.devui.deployment.menu.ConfigurationProcessor#registerConfigs", "started": "12:12:55.284", "dependents": [532], "id": 453, "thread": "build-18"}, {"duration": 19, "stepId": "io.quarkus.hibernate.orm.deployment.metrics.HibernateOrmMetricsProcessor#metrics", "started": "12:12:54.364", "dependents": [532], "id": 278, "thread": "build-14"}, {"duration": 19, "stepId": "io.quarkus.deployment.steps.ThreadPoolSetup#createExecutor", "started": "12:12:54.373", "dependents": [532, 283, 289, 523, 284, 292, 301], "id": 282, "thread": "build-4"}, {"duration": 18, "stepId": "io.quarkus.rest.client.reactive.jackson.deployment.RestClientReactiveJacksonProcessor#additionalProviders_d467f0796ff6c3d28e57d6f18c92f27cf1d4298e", "started": "12:12:54.164", "dependents": [377], "id": 64, "thread": "build-27"}, {"duration": 18, "stepId": "io.quarkus.devservices.postgresql.deployment.PostgresqlDevServicesProcessor#setupPostgres", "started": "12:12:54.259", "dependents": [385], "id": 207, "thread": "build-23"}, {"duration": 17, "stepId": "io.quarkus.jaxrs.client.reactive.deployment.JaxrsClientReactiveProcessor#registerInvocationCallbacks", "started": "12:12:54.737", "dependents": [532], "id": 347, "thread": "build-14"}, {"duration": 17, "stepId": "io.quarkus.rest.client.reactive.deployment.RestClientReactiveProcessor#setUpDefaultMediaType", "started": "12:12:54.259", "dependents": [510], "id": 206, "thread": "build-42"}, {"duration": 17, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#fileHandling", "started": "12:12:54.209", "dependents": [511, 510, 509], "id": 142, "thread": "build-9"}, {"duration": 17, "stepId": "io.quarkus.smallrye.reactivemessaging.deployment.ReactiveMessagingMethodsProcessor#reactiveMessagingMethods", "started": "12:12:54.175", "dependents": [320], "id": 85, "thread": "build-16"}, {"duration": 17, "stepId": "io.quarkus.rest.client.reactive.deployment.RestClientReactiveProcessor#registerCompressionInterceptors", "started": "12:12:54.205", "dependents": [528], "id": 138, "thread": "build-59"}, {"duration": 17, "stepId": "io.quarkus.jdbc.postgresql.deployment.JDBCPostgreSQLProcessor#devDbHandler", "started": "12:12:54.172", "dependents": [385], "id": 81, "thread": "build-21"}, {"duration": 17, "stepId": "io.quarkus.agroal.deployment.AgroalProcessor#registerRowSetSupport", "started": "12:12:54.172", "dependents": [528], "id": 79, "thread": "build-3"}, {"duration": 17, "stepId": "io.quarkus.arc.deployment.devui.ArcDevUIProcessor#registerMonitoringComponents", "started": "12:12:54.336", "dependents": [421, 406], "id": 240, "thread": "build-10"}, {"duration": 16, "stepId": "io.quarkus.smallrye.openapi.deployment.SmallRyeOpenApiProcessor#registerOpenApiSchemaClassesForReflection", "started": "12:12:54.940", "dependents": [528, 512], "id": 419, "thread": "build-34"}, {"duration": 16, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveScanningProcessor#defaultUnwrappedException", "started": "12:12:54.193", "dependents": [381], "id": 106, "thread": "build-19"}, {"duration": 16, "stepId": "io.quarkus.devui.deployment.DevUIProcessor#additionalBean", "started": "12:12:54.391", "dependents": [316, 421, 406], "id": 286, "thread": "build-14"}, {"duration": 15, "stepId": "io.quarkus.hibernate.orm.deployment.GraalVMFeatures#registerGeneratorClassesForReflections", "started": "12:12:54.174", "dependents": [528], "id": 82, "thread": "build-19"}, {"duration": 15, "stepId": "io.quarkus.resteasy.reactive.jackson.deployment.processor.ResteasyReactiveJacksonProcessor#resolveRolesAllowedConfigExpressions", "started": "12:12:54.737", "dependents": [463, 454, 532, 449, 450], "id": 344, "thread": "build-56"}, {"duration": 15, "stepId": "io.quarkus.scheduler.deployment.SchedulerProcessor#beans", "started": "12:12:54.285", "dependents": [421, 406], "id": 231, "thread": "build-55"}, {"duration": 15, "stepId": "io.quarkus.jaxrs.client.reactive.deployment.JaxrsClientReactiveProcessor#setupClientProxies", "started": "12:12:56.303", "dependents": [529, 528, 532, 512], "id": 510, "thread": "build-116"}, {"duration": 15, "stepId": "io.quarkus.config.yaml.deployment.ConfigYamlProcessor#yamlConfig", "started": "12:12:54.193", "dependents": [472], "id": 104, "thread": "build-7"}, {"duration": 15, "stepId": "io.quarkus.swaggerui.deployment.SwaggerUiProcessor#feature", "started": "12:12:54.259", "dependents": [532], "id": 202, "thread": "build-68"}, {"duration": 15, "stepId": "io.quarkus.deployment.dev.HotDeploymentWatchedFileBuildStep#setupWatchedFileHotDeployment", "started": "12:12:55.232", "dependents": [524, 526], "id": 446, "thread": "build-29"}, {"duration": 15, "stepId": "io.quarkus.smallrye.reactivemessaging.deployment.SmallRyeReactiveMessagingProcessor#devmodeSupport", "started": "12:12:54.737", "dependents": [421, 406], "id": 346, "thread": "build-19"}, {"duration": 15, "stepId": "io.quarkus.hibernate.orm.deployment.dev.HibernateOrmDevUIProcessor#create", "started": "12:12:54.158", "dependents": [476, 477], "id": 42, "thread": "build-16"}, {"duration": 15, "stepId": "io.quarkus.smallrye.openapi.deployment.SmallRyeOpenApiProcessor#additionalBean", "started": "12:12:54.172", "dependents": [421, 406], "id": 75, "thread": "build-10"}, {"duration": 15, "stepId": "io.quarkus.resteasy.reactive.server.deployment.devui.ResteasyReactiveDevUIProcessor#createJsonRPCService", "started": "12:12:54.199", "dependents": [286, 364], "id": 124, "thread": "build-39"}, {"duration": 15, "stepId": "io.quarkus.smallrye.reactivemessaging.deployment.WiringProcessor#extractComponents", "started": "12:12:55.203", "dependents": [481, 443, 448, 441, 453, 452], "id": 440, "thread": "build-56"}, {"duration": 14, "stepId": "io.quarkus.smallrye.openapi.deployment.SmallRyeOpenApiProcessor#registerAnnotatedUserDefinedRuntimeFilters", "started": "12:12:54.940", "dependents": [454, 528, 532, 449, 450], "id": 418, "thread": "build-26"}, {"duration": 14, "stepId": "io.quarkus.smallrye.openapi.deployment.SmallRyeOpenApiProcessor#registerAutoSecurityFilter", "started": "12:12:54.940", "dependents": [454, 532, 449, 450], "id": 417, "thread": "build-63"}, {"duration": 14, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#preinitializeRouter", "started": "12:12:54.493", "dependents": [454, 532, 449, 521, 450], "id": 305, "thread": "build-24"}, {"duration": 14, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#contributeQuarkusConfigToJpaModel", "started": "12:12:54.259", "dependents": [386], "id": 198, "thread": "build-21"}, {"duration": 14, "stepId": "io.quarkus.deployment.steps.CapabilityAggregationStep#provideCapabilities", "started": "12:12:54.264", "dependents": [215], "id": 208, "thread": "build-69"}, {"duration": 14, "stepId": "io.quarkus.jackson.deployment.JacksonProcessor#unremovable", "started": "12:12:54.737", "dependents": [421, 406, 469, 461], "id": 338, "thread": "build-31"}, {"duration": 14, "stepId": "io.quarkus.arc.deployment.ConfigStaticInitBuildSteps#registerBeans", "started": "12:12:54.149", "dependents": [421, 406], "id": 17, "thread": "build-12"}, {"duration": 14, "stepId": "io.quarkus.netty.deployment.NettyProcessor#build", "started": "12:12:54.259", "dependents": [528, 294], "id": 204, "thread": "build-15"}, {"duration": 14, "stepId": "io.quarkus.jackson.deployment.JacksonProcessor#supportMixins", "started": "12:12:54.737", "dependents": [454, 528, 532, 449, 450], "id": 340, "thread": "build-37"}, {"duration": 14, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#httpRoot", "started": "12:12:54.259", "dependents": [522, 501, 530, 523, 297, 519, 513], "id": 199, "thread": "build-56"}, {"duration": 14, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#frameworkRoot", "started": "12:12:54.259", "dependents": [494, 523, 297, 519, 513, 503, 225, 518, 521, 501, 499, 471, 480, 203, 205, 423], "id": 200, "thread": "build-26"}, {"duration": 14, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#providersFromClasspath", "started": "12:12:54.182", "dependents": [511, 510, 509, 508], "id": 91, "thread": "build-39"}, {"duration": 13, "stepId": "io.quarkus.vertx.deployment.VertxProcessor#registerVerticleClasses", "started": "12:12:54.736", "dependents": [528], "id": 337, "thread": "build-24"}, {"duration": 13, "stepId": "io.quarkus.arc.deployment.BuildTimeEnabledProcessor#buildExclusions", "started": "12:12:54.752", "dependents": [416], "id": 350, "thread": "build-37"}, {"duration": 13, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#build_9d6b7122fb368970c50c3a870d1f672392cd8afb", "started": "12:12:54.175", "dependents": [528, 294], "id": 77, "thread": "build-7"}, {"duration": 13, "stepId": "io.quarkus.deployment.dev.testing.TestTracingProcessor#sharedStateListener", "started": "12:12:54.200", "dependents": [309], "id": 123, "thread": "build-54"}, {"duration": 13, "stepId": "io.quarkus.redis.deployment.client.RedisClientProcessor#init", "started": "12:12:55.218", "dependents": [446, 454, 532, 449, 450], "id": 445, "thread": "build-18"}, {"duration": 13, "stepId": "io.quarkus.smallrye.reactivemessaging.deployment.WiringProcessor#discoverConnectors", "started": "12:12:55.203", "dependents": [443, 451], "id": 437, "thread": "build-29"}, {"duration": 13, "stepId": "io.quarkus.rest.client.reactive.deployment.RestClientReactiveProcessor#unremovableBeans", "started": "12:12:54.175", "dependents": [469, 461], "id": 80, "thread": "build-31"}, {"duration": 13, "stepId": "io.quarkus.smallrye.openapi.deployment.devui.OpenApiDevUIProcessor#pages", "started": "12:12:54.274", "dependents": [476, 477], "id": 225, "thread": "build-21"}, {"duration": 13, "stepId": "io.quarkus.netty.deployment.NettyProcessor#limitArenaSize", "started": "12:12:54.259", "dependents": [532], "id": 196, "thread": "build-9"}, {"duration": 13, "stepId": "io.quarkus.smallrye.openapi.deployment.SmallRyeOpenApiProcessor#smallryeOpenApiIndex", "started": "12:12:54.927", "dependents": [420, 530, 419, 417, 418], "id": 416, "thread": "build-36"}, {"duration": 13, "stepId": "io.quarkus.deployment.steps.NativeImageConfigBuildStep#build", "started": "12:12:54.421", "dependents": [532], "id": 294, "thread": "build-2"}, {"duration": 13, "stepId": "io.quarkus.scheduler.deployment.SchedulerProcessor#collectScheduledMethods_84ea631eea52cbbcaee3e56019e68e7826861add", "started": "12:12:55.203", "dependents": [438, 473, 447], "id": 436, "thread": "build-63"}, {"duration": 13, "stepId": "io.quarkus.scheduler.deployment.SchedulerProcessor#metrics", "started": "12:12:54.259", "dependents": [421], "id": 197, "thread": "build-45"}, {"duration": 12, "stepId": "io.quarkus.arc.deployment.BuildTimeEnabledProcessor#unlessBuildProperty", "started": "12:12:54.739", "dependents": [350, 345, 365], "id": 343, "thread": "build-18"}, {"duration": 12, "stepId": "io.quarkus.deployment.steps.CombinedIndexBuildStep#build", "started": "12:12:54.724", "dependents": [333, 343, 382, 337, 369, 317, 372, 364, 342, 381, 319, 506, 344, 318, 379, 320, 399, 331, 338, 429, 363, 497, 348, 426, 349, 335, 321, 322, 368, 351, 330, 347, 413, 465, 360, 367, 341, 336, 324, 437, 340, 388, 329, 496, 512, 390, 365, 323, 371, 339, 416, 378, 370, 346, 334, 361, 394, 332, 362, 377, 328, 421, 391, 327, 326, 366, 374, 325], "id": 316, "thread": "build-66"}, {"duration": 12, "stepId": "io.quarkus.deployment.steps.DevModeBuildStep#watchChanges", "started": "12:12:54.259", "dependents": [446], "id": 194, "thread": "build-31"}, {"duration": 12, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveScanningProcessor#compressionSupport", "started": "12:12:54.259", "dependents": [500], "id": 193, "thread": "build-52"}, {"duration": 12, "stepId": "io.quarkus.arc.deployment.BuildTimeEnabledProcessor#unlessBuildProfile", "started": "12:12:54.739", "dependents": [350, 345, 365], "id": 342, "thread": "build-68"}, {"duration": 12, "stepId": "io.quarkus.rest.client.reactive.deployment.devconsole.RestClientReactiveDevUIProcessor#createJsonRPCServiceForCache", "started": "12:12:54.160", "dependents": [286, 364], "id": 35, "thread": "build-21"}, {"duration": 12, "stepId": "io.quarkus.hibernate.orm.panache.deployment.PanacheHibernateResourceProcessor#build", "started": "12:12:56.172", "dependents": [529, 498], "id": 497, "thread": "build-116"}, {"duration": 12, "stepId": "io.quarkus.arc.deployment.BuildTimeEnabledProcessor#ifBuildProperty", "started": "12:12:54.739", "dependents": [350, 345, 365], "id": 339, "thread": "build-35"}, {"duration": 12, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#registerVerticleClasses", "started": "12:12:54.737", "dependents": [528], "id": 335, "thread": "build-40"}, {"duration": 12, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveScanningProcessor#cacheControlSupport", "started": "12:12:54.149", "dependents": [500], "id": 12, "thread": "build-9"}, {"duration": 12, "stepId": "io.quarkus.scheduler.deployment.devui.SchedulerDevUIProcessor#rpcProvider", "started": "12:12:54.200", "dependents": [286, 364], "id": 116, "thread": "build-53"}, {"duration": 12, "stepId": "io.quarkus.smallrye.openapi.deployment.SmallRyeOpenApiProcessor#runtimeOnly", "started": "12:12:54.193", "dependents": [472], "id": 99, "thread": "build-23"}, {"duration": 12, "stepId": "io.quarkus.arc.deployment.BuildTimeEnabledProcessor#ifBuildProfile", "started": "12:12:54.739", "dependents": [350, 345, 365], "id": 341, "thread": "build-48"}, {"duration": 12, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#doNotRemoveVertxOptionsCustomizers", "started": "12:12:54.161", "dependents": [469, 461], "id": 44, "thread": "build-5"}, {"duration": 12, "stepId": "io.quarkus.arc.deployment.ArcProcessor#initializeContainer", "started": "12:12:55.915", "dependents": [532, 483], "id": 482, "thread": "build-149"}, {"duration": 12, "stepId": "io.quarkus.smallrye.reactivemessaging.deployment.SmallRyeReactiveMessagingProcessor#duplicatedContextSupport", "started": "12:12:54.737", "dependents": [421, 406], "id": 336, "thread": "build-69"}, {"duration": 12, "stepId": "io.quarkus.deployment.logging.LoggingResourceProcessor#setUpDefaultLevels", "started": "12:12:54.259", "dependents": [382, 472], "id": 192, "thread": "build-37"}, {"duration": 11, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#runtimeConfiguration", "started": "12:12:56.640", "dependents": [517, 532], "id": 516, "thread": "build-75"}, {"duration": 11, "stepId": "io.quarkus.redis.deployment.client.RedisClientProcessor#makeHostsProviderAndOptionsCustomizerUnremovable", "started": "12:12:54.161", "dependents": [469, 461], "id": 37, "thread": "build-7"}, {"duration": 11, "stepId": "io.quarkus.resteasy.reactive.common.deployment.ResteasyReactiveCommonProcessor#checkMixingStacks", "started": "12:12:54.285", "dependents": [524, 526], "id": 230, "thread": "build-68"}, {"duration": 11, "stepId": "io.quarkus.smallrye.openapi.deployment.SmallRyeOpenApiProcessor#logCleanup", "started": "12:12:54.161", "dependents": [382, 160], "id": 33, "thread": "build-15"}, {"duration": 11, "stepId": "io.quarkus.deployment.steps.ReflectiveHierarchyStep#ignoreJavaClassWarnings", "started": "12:12:54.189", "dependents": [512], "id": 94, "thread": "build-42"}, {"duration": 11, "stepId": "io.quarkus.deployment.ide.IdeProcessor#detectIdeFiles", "started": "12:12:54.161", "dependents": [261], "id": 34, "thread": "build-17"}, {"duration": 11, "stepId": "io.quarkus.rest.client.reactive.deployment.RestClientReactiveProcessor#announceFeature", "started": "12:12:54.149", "dependents": [532], "id": 8, "thread": "build-11"}, {"duration": 11, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#additionalBeans", "started": "12:12:54.149", "dependents": [421, 406], "id": 11, "thread": "build-13"}, {"duration": 11, "stepId": "io.quarkus.deployment.logging.LoggingResourceProcessor#setupLogFilters", "started": "12:12:54.161", "dependents": [382, 160], "id": 31, "thread": "build-3"}, {"duration": 11, "stepId": "io.quarkus.agroal.deployment.AgroalMetricsProcessor#registerMetrics", "started": "12:12:54.421", "dependents": [532], "id": 293, "thread": "build-31"}, {"duration": 10, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveScanningProcessor#scanForParamConverters_dcdfdd2a310a09abe5ee3f0ed2b2bc49f36f3d07", "started": "12:12:54.780", "dependents": [528, 421, 406, 514, 500], "id": 376, "thread": "build-37"}, {"duration": 10, "stepId": "io.quarkus.deployment.steps.AdditionalClassLoaderResourcesBuildStep#appendAdditionalClassloaderResources", "started": "12:12:54.150", "dependents": [316], "id": 10, "thread": "build-15"}, {"duration": 10, "stepId": "io.quarkus.deployment.logging.LoggingResourceProcessor#setMinLevelForInitialConfigurator", "started": "12:12:54.259", "dependents": [532], "id": 190, "thread": "build-41"}, {"duration": 10, "stepId": "io.quarkus.arc.deployment.SyntheticBeansProcessor#initRegular", "started": "12:12:55.265", "dependents": [458], "id": 450, "thread": "build-18"}, {"duration": 10, "stepId": "io.quarkus.smallrye.reactivemessaging.rabbitmq.deployment.devui.RabbitDevUIProcessor#createCard", "started": "12:12:54.161", "dependents": [476, 477], "id": 26, "thread": "build-13"}, {"duration": 10, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveScanningProcessor#scanForContextResolvers", "started": "12:12:54.776", "dependents": [507, 528, 421, 406, 514], "id": 372, "thread": "build-29"}, {"duration": 10, "stepId": "io.quarkus.jackson.deployment.JacksonProcessor#jacksonSupport", "started": "12:12:54.737", "dependents": [454, 532, 449, 450], "id": 334, "thread": "build-8"}, {"duration": 10, "stepId": "io.quarkus.jdbc.postgresql.deployment.JDBCPostgreSQLProcessor#registerDriver", "started": "12:12:54.264", "dependents": [291], "id": 201, "thread": "build-55"}, {"duration": 10, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#logging", "started": "12:12:54.214", "dependents": [192], "id": 141, "thread": "build-58"}, {"duration": 9, "stepId": "io.quarkus.deployment.ide.IdeProcessor#effectiveIde", "started": "12:12:54.359", "dependents": [270, 308, 314, 480], "id": 261, "thread": "build-34"}, {"duration": 9, "stepId": "io.quarkus.arc.deployment.ArcProcessor#marker", "started": "12:12:54.148", "dependents": [312], "id": 5, "thread": "build-3"}, {"duration": 9, "stepId": "io.quarkus.hibernate.orm.panache.deployment.PanacheHibernateResourceProcessor#produceModel", "started": "12:12:54.148", "dependents": [332, 531, 495], "id": 4, "thread": "build-2"}, {"duration": 9, "stepId": "io.quarkus.deployment.recording.substitutions.AdditionalSubstitutionsBuildStep#additionalSubstitutions", "started": "12:12:54.223", "dependents": [532], "id": 148, "thread": "build-26"}, {"duration": 9, "stepId": "io.quarkus.deployment.pkg.steps.FileSystemResourcesBuildStep#notNormalMode", "started": "12:12:54.278", "dependents": [], "id": 227, "thread": "build-42"}, {"duration": 9, "stepId": "io.quarkus.arc.deployment.ConfigBuildStep#generateConfigProperties", "started": "12:12:54.736", "dependents": [528, 469, 426, 429, 467], "id": 333, "thread": "build-33"}, {"duration": 9, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#detectBasicAuthImplicitlyRequired", "started": "12:12:55.203", "dependents": [532], "id": 435, "thread": "build-2"}, {"duration": 9, "stepId": "io.quarkus.vertx.deployment.VertxProcessor#registerBean", "started": "12:12:54.148", "dependents": [421, 406], "id": 6, "thread": "build-4"}, {"duration": 9, "stepId": "io.quarkus.arc.deployment.staticmethods.InterceptedStaticMethodsProcessor#collectInterceptedStaticMethods", "started": "12:12:55.203", "dependents": [469, 461, 485, 439], "id": 433, "thread": "build-47"}, {"duration": 9, "stepId": "io.quarkus.devui.deployment.DevUIProcessor#createJsonRpcRouter", "started": "12:12:55.933", "dependents": [532], "id": 491, "thread": "build-116"}, {"duration": 9, "stepId": "io.quarkus.vertx.deployment.VertxProcessor#collectEventConsumers", "started": "12:12:55.203", "dependents": [442, 458], "id": 434, "thread": "build-6"}, {"duration": 9, "stepId": "io.quarkus.redis.deployment.client.RedisDatasourceProcessor#detectUsage", "started": "12:12:55.203", "dependents": [445, 444], "id": 432, "thread": "build-18"}, {"duration": 9, "stepId": "io.quarkus.swaggerui.deployment.SwaggerUiProcessor#registerSwaggerUiHandler", "started": "12:12:55.975", "dependents": [532, 521, 520], "id": 494, "thread": "build-116"}, {"duration": 9, "stepId": "io.quarkus.jaxrs.client.reactive.deployment.JaxrsClientReactiveProcessor#initializeStorkFilter", "started": "12:12:54.240", "dependents": [528, 316, 421, 406], "id": 153, "thread": "build-45"}, {"duration": 8, "stepId": "io.quarkus.smallrye.openapi.deployment.SmallRyeOpenApiProcessor#configFiles", "started": "12:12:54.278", "dependents": [446], "id": 226, "thread": "build-19"}, {"duration": 8, "stepId": "io.quarkus.arc.deployment.ConfigBuildStep#registerConfigRootsAsBeans", "started": "12:12:54.259", "dependents": [454, 449, 450], "id": 188, "thread": "build-30"}, {"duration": 8, "stepId": "io.quarkus.jackson.deployment.JacksonProcessor#register", "started": "12:12:54.737", "dependents": [528, 421, 406, 512], "id": 331, "thread": "build-2"}, {"duration": 8, "stepId": "io.quarkus.resteasy.reactive.jackson.deployment.processor.ResteasyReactiveJacksonProcessor#exceptionMappers", "started": "12:12:54.172", "dependents": [381], "id": 56, "thread": "build-28"}, {"duration": 8, "stepId": "io.quarkus.deployment.steps.MainClassBuildStep#applicationReflection", "started": "12:12:54.148", "dependents": [528], "id": 2, "thread": "build-7"}, {"duration": 8, "stepId": "io.quarkus.arc.deployment.ReflectiveBeanClassesProcessor#implicitReflectiveBeanClasses", "started": "12:12:55.203", "dependents": [481], "id": 431, "thread": "build-36"}, {"duration": 8, "stepId": "io.quarkus.smallrye.context.deployment.SmallRyeContextPropagationProcessor#transformInjectionPoint", "started": "12:12:54.148", "dependents": [421], "id": 3, "thread": "build-5"}, {"duration": 8, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#responseHeaderSupport", "started": "12:12:54.187", "dependents": [500], "id": 90, "thread": "build-41"}, {"duration": 8, "stepId": "io.quarkus.deployment.dev.ConfigureDisableInstrumentationBuildStep#configure", "started": "12:12:54.164", "dependents": [524, 526], "id": 40, "thread": "build-11"}, {"duration": 8, "stepId": "io.quarkus.datasource.deployment.devservices.DevServicesDatasourceProcessor#launchDatabases", "started": "12:12:54.849", "dependents": [400, 404], "id": 385, "thread": "build-26"}, {"duration": 8, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#configurationDescriptorBuilding", "started": "12:12:54.877", "dependents": [446, 398, 397, 532, 531, 402, 396, 395, 403, 399], "id": 394, "thread": "build-2"}, {"duration": 8, "stepId": "io.quarkus.deployment.dev.IsolatedDevModeMain$5$1@4d91c252", "started": "12:12:54.164", "dependents": [421, 500], "id": 29, "thread": "build-25"}, {"duration": 8, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#jpaEntitiesIndexer", "started": "12:12:54.737", "dependents": [386, 531], "id": 332, "thread": "build-25"}, {"duration": 7, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmCdiProcessor#generateDataSourceBeans", "started": "12:12:54.885", "dependents": [454, 532, 421, 406, 449, 450], "id": 403, "thread": "build-37"}, {"duration": 7, "stepId": "io.quarkus.arc.deployment.ArcProcessor#registerSyntheticObservers", "started": "12:12:55.319", "dependents": [481, 460, 528, 469, 461, 459], "id": 458, "thread": "build-63"}, {"duration": 7, "stepId": "io.quarkus.resteasy.reactive.common.deployment.ResteasyReactiveCommonProcessor#scanForParameterContainers", "started": "12:12:54.776", "dependents": [510, 500], "id": 371, "thread": "build-52"}, {"duration": 7, "stepId": "io.quarkus.resteasy.reactive.jackson.deployment.processor.ResteasyReactiveJacksonProcessor#jsonDefault", "started": "12:12:54.172", "dependents": [500], "id": 54, "thread": "build-17"}, {"duration": 7, "stepId": "io.quarkus.deployment.dev.testing.TestTracingProcessor#startTesting", "started": "12:12:54.519", "dependents": [524, 526, 382], "id": 309, "thread": "build-9"}, {"duration": 7, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#build_7a4403d699506d83ac39616f3c11e5e1b448d863", "started": "12:12:54.885", "dependents": [532, 483], "id": 402, "thread": "build-61"}, {"duration": 7, "stepId": "io.quarkus.deployment.steps.ConfigGenerationBuildStep#unknownConfigFiles", "started": "12:12:54.724", "dependents": [532], "id": 315, "thread": "build-33"}, {"duration": 7, "stepId": "io.quarkus.vertx.deployment.VertxProcessor#build", "started": "12:12:55.212", "dependents": [524, 445, 528, 526, 532, 444, 457], "id": 442, "thread": "build-47"}, {"duration": 6, "stepId": "io.quarkus.deployment.logging.LoggingResourceProcessor#setUpDarkeningDefault", "started": "12:12:54.166", "dependents": [472], "id": 38, "thread": "build-19"}, {"duration": 6, "stepId": "io.quarkus.arc.deployment.SplitPackageProcessor#splitPackageDetection", "started": "12:12:54.724", "dependents": [481], "id": 313, "thread": "build-24"}, {"duration": 6, "stepId": "io.quarkus.deployment.logging.LoggingResourceProcessor#setupStackTraceFormatter", "started": "12:12:54.724", "dependents": [382], "id": 314, "thread": "build-55"}, {"duration": 6, "stepId": "io.quarkus.resteasy.reactive.jackson.deployment.processor.ResteasyReactiveJacksonProcessor#beans", "started": "12:12:54.205", "dependents": [421, 406], "id": 114, "thread": "build-23"}, {"duration": 6, "stepId": "io.quarkus.panache.common.deployment.PanacheHibernateCommonResourceProcessor#findEntityClasses", "started": "12:12:54.866", "dependents": [496], "id": 390, "thread": "build-2"}, {"duration": 6, "stepId": "io.quarkus.devui.deployment.welcome.WelcomeProcessor#createWelcomePages", "started": "12:12:55.853", "dependents": [480], "id": 479, "thread": "build-124"}, {"duration": 6, "stepId": "io.quarkus.devui.deployment.logstream.LogStreamProcessor#additionalBean", "started": "12:12:54.163", "dependents": [421, 406], "id": 25, "thread": "build-24"}, {"duration": 6, "stepId": "io.quarkus.deployment.steps.CapabilityAggregationStep#aggregateCapabilities", "started": "12:12:54.278", "dependents": [515, 259, 216, 530, 260, 419, 393, 527, 218, 385, 223, 381, 512, 344, 272, 307, 399, 338, 217, 363, 291, 514, 510, 378, 219, 392, 288, 349, 255, 403, 361, 394, 230, 231, 295, 229, 421, 473, 262, 220, 500, 228, 402, 222], "id": 215, "thread": "build-23"}, {"duration": 6, "stepId": "io.quarkus.jdbc.postgresql.deployment.JDBCPostgreSQLProcessor#registerServiceBinding", "started": "12:12:54.285", "dependents": [385, 291], "id": 229, "thread": "build-26"}, {"duration": 6, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveScanningProcessor#scanForExceptionMappers", "started": "12:12:54.834", "dependents": [528, 421, 406, 514], "id": 381, "thread": "build-26"}, {"duration": 6, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#hotDeploymentWatchedFiles", "started": "12:12:54.259", "dependents": [446], "id": 186, "thread": "build-54"}, {"duration": 5, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#integrateEagerSecurity", "started": "12:12:54.767", "dependents": [500], "id": 363, "thread": "build-37"}, {"duration": 5, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveCDIProcessor#additionalBeans", "started": "12:12:54.782", "dependents": [528, 421, 406], "id": 373, "thread": "build-36"}, {"duration": 5, "stepId": "io.quarkus.arc.deployment.ConfigBuildStep#validateConfigMappingsInjectionPoints", "started": "12:12:55.482", "dependents": [472, 470], "id": 469, "thread": "build-36"}, {"duration": 5, "stepId": "io.quarkus.resteasy.reactive.jackson.deployment.processor.ResteasyReactiveJacksonProcessor#additionalProviders", "started": "12:12:56.297", "dependents": [511, 510, 509, 508], "id": 507, "thread": "build-141"}, {"duration": 5, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveScanningProcessor#scanForFeatures", "started": "12:12:54.776", "dependents": [373, 514], "id": 369, "thread": "build-36"}, {"duration": 5, "stepId": "io.quarkus.resteasy.reactive.common.deployment.ResteasyReactiveCommonProcessor#setupEndpoints", "started": "12:12:55.933", "dependents": [528, 511, 510, 509, 508], "id": 490, "thread": "build-30"}, {"duration": 5, "stepId": "io.quarkus.resteasy.reactive.common.deployment.ResteasyReactiveCommonProcessor#buildResourceInterceptors", "started": "12:12:54.834", "dependents": [421, 406, 514, 415, 500], "id": 380, "thread": "build-61"}, {"duration": 5, "stepId": "io.quarkus.jdbc.postgresql.deployment.JDBCPostgreSQLProcessor#configureAgroalConnection", "started": "12:12:54.285", "dependents": [421, 406], "id": 228, "thread": "build-52"}, {"duration": 5, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveScanningProcessor#scanForDynamicFeatures", "started": "12:12:54.776", "dependents": [373, 514], "id": 370, "thread": "build-47"}, {"duration": 5, "stepId": "io.quarkus.devui.deployment.ide.IdeProcessor#createJsonRPCService", "started": "12:12:54.369", "dependents": [281], "id": 270, "thread": "build-36"}, {"duration": 5, "stepId": "io.quarkus.netty.deployment.NettyProcessor#registerEventLoopBeans", "started": "12:12:54.493", "dependents": [454, 532, 449, 450], "id": 304, "thread": "build-55"}, {"duration": 5, "stepId": "io.quarkus.deployment.steps.BlockingOperationControlBuildStep#blockingOP", "started": "12:12:54.373", "dependents": [532], "id": 276, "thread": "build-64"}, {"duration": 5, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#initializeRouter", "started": "12:12:56.767", "dependents": [522, 532, 523], "id": 521, "thread": "build-178"}, {"duration": 5, "stepId": "io.quarkus.arc.deployment.SyntheticBeansProcessor#initStatic", "started": "12:12:55.265", "dependents": [532, 458], "id": 449, "thread": "build-29"}, {"duration": 5, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#registerHttpAuthMechanismAnnotations", "started": "12:12:54.214", "dependents": [349], "id": 130, "thread": "build-53"}, {"duration": 5, "stepId": "io.quarkus.arc.deployment.TestsAsBeansProcessor#testAnnotations", "started": "12:12:54.174", "dependents": [421, 406, 240], "id": 52, "thread": "build-5"}, {"duration": 5, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#transformEndpoints", "started": "12:12:54.927", "dependents": [421], "id": 415, "thread": "build-2"}, {"duration": 5, "stepId": "io.quarkus.arc.deployment.WrongAnnotationUsageProcessor#detect", "started": "12:12:55.203", "dependents": [481], "id": 430, "thread": "build-37"}, {"duration": 5, "stepId": "io.quarkus.redis.deployment.client.DevServicesRedisProcessor#startRedisContainers", "started": "12:12:54.849", "dependents": [400, 404], "id": 384, "thread": "build-61"}, {"duration": 5, "stepId": "io.quarkus.smallrye.reactivemessaging.deployment.SmallRyeReactiveMessagingProcessor#produceCoroutineScope", "started": "12:12:54.197", "dependents": [421, 406], "id": 96, "thread": "build-51"}, {"duration": 5, "stepId": "io.quarkus.devservices.deployment.DevServicesProcessor#config", "started": "12:12:54.889", "dependents": [405, 502], "id": 404, "thread": "build-63"}, {"duration": 5, "stepId": "io.quarkus.deployment.logging.LoggingResourceProcessor#setUpDefaultLogCleanupFilters", "started": "12:12:54.250", "dependents": [472], "id": 160, "thread": "build-40"}, {"duration": 5, "stepId": "io.quarkus.netty.deployment.NettyProcessor#cleanupMacDNSInLog", "started": "12:12:54.174", "dependents": [382, 160], "id": 53, "thread": "build-8"}, {"duration": 5, "stepId": "io.quarkus.smallrye.reactivemessaging.rabbitmq.deployment.RabbitMQDevServicesProcessor#startRabbitMQDevService", "started": "12:12:54.849", "dependents": [400, 404], "id": 383, "thread": "build-18"}, {"duration": 5, "stepId": "io.quarkus.redis.deployment.client.RedisDatasourceProcessor#init", "started": "12:12:55.218", "dependents": [454, 532, 449, 450], "id": 444, "thread": "build-29"}, {"duration": 4, "stepId": "io.quarkus.smallrye.reactivemessaging.deployment.devui.ReactiveMessagingDevUIProcessor#beans", "started": "12:12:54.164", "dependents": [421, 406], "id": 24, "thread": "build-12"}, {"duration": 4, "stepId": "io.quarkus.arc.deployment.ConfigBuildStep#additionalBeans", "started": "12:12:54.219", "dependents": [421, 406], "id": 135, "thread": "build-67"}, {"duration": 4, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#convertRoutes", "started": "12:12:56.761", "dependents": [521, 520], "id": 519, "thread": "build-163"}, {"duration": 4, "stepId": "io.quarkus.deployment.console.ConsoleProcessor#setupExceptionHandler", "started": "12:12:54.519", "dependents": [314], "id": 308, "thread": "build-24"}, {"duration": 4, "stepId": "io.quarkus.netty.deployment.NettyProcessor#registerQualifiers", "started": "12:12:54.214", "dependents": [421, 406], "id": 129, "thread": "build-33"}, {"duration": 4, "stepId": "io.quarkus.deployment.CollectionClassProcessor#setupCollectionClasses", "started": "12:12:54.198", "dependents": [528], "id": 98, "thread": "build-52"}, {"duration": 4, "stepId": "io.quarkus.scheduler.deployment.SchedulerProcessor#unremovableBeans", "started": "12:12:54.179", "dependents": [469, 461], "id": 63, "thread": "build-37"}, {"duration": 4, "stepId": "io.quarkus.datasource.deployment.devui.DevUIDatasourceProcessor#create", "started": "12:12:54.259", "dependents": [476, 477], "id": 177, "thread": "build-43"}, {"duration": 4, "stepId": "io.quarkus.narayana.jta.deployment.NarayanaJtaProcessor#logCleanupFilters", "started": "12:12:54.167", "dependents": [382, 160], "id": 32, "thread": "build-28"}, {"duration": 4, "stepId": "io.quarkus.arc.deployment.LifecycleEventsBuildStep#startupEvent", "started": "12:12:56.868", "dependents": [527, 532], "id": 526, "thread": "build-178"}, {"duration": 4, "stepId": "io.quarkus.panache.common.deployment.PanacheHibernateCommonResourceProcessor#replaceFieldAccesses", "started": "12:12:56.172", "dependents": [529], "id": 496, "thread": "build-30"}, {"duration": 4, "stepId": "io.quarkus.arc.deployment.UnremovableAnnotationsProcessor#unremovableBeans", "started": "12:12:54.219", "dependents": [469, 461], "id": 137, "thread": "build-66"}, {"duration": 4, "stepId": "io.quarkus.arc.deployment.TestsAsBeansProcessor#testClassBeans", "started": "12:12:54.175", "dependents": [421, 406], "id": 55, "thread": "build-32"}, {"duration": 4, "stepId": "io.quarkus.vertx.deployment.EventBusCodecProcessor#registerCodecs", "started": "12:12:54.927", "dependents": [528, 442], "id": 413, "thread": "build-34"}, {"duration": 4, "stepId": "io.quarkus.deployment.steps.CurateOutcomeBuildStep#curateOutcome", "started": "12:12:54.259", "dependents": [291, 312, 477, 215, 208, 479, 364, 187, 310, 230, 492, 385, 529, 214, 257, 476, 281, 499, 480, 331, 182], "id": 179, "thread": "build-55"}, {"duration": 4, "stepId": "io.quarkus.arc.deployment.ConfigBuildStep#registerConfigMappingsBean", "started": "12:12:55.203", "dependents": [458], "id": 429, "thread": "build-48"}, {"duration": 4, "stepId": "io.quarkus.narayana.jta.deployment.NarayanaJtaProcessor#unremovableBean", "started": "12:12:54.223", "dependents": [469, 461], "id": 147, "thread": "build-70"}, {"duration": 4, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveScanningProcessor#scanForParamConverters_59e3169e3a646b7fcf3083416f558434b73816c5", "started": "12:12:54.776", "dependents": [376], "id": 368, "thread": "build-6"}, {"duration": 4, "stepId": "io.quarkus.devui.deployment.BuildTimeContentProcessor#mapPageBuildTimeData", "started": "12:12:55.563", "dependents": [499], "id": 476, "thread": "build-47"}, {"duration": 4, "stepId": "io.quarkus.deployment.SslProcessor#setupNativeSsl", "started": "12:12:54.259", "dependents": [295, 201, 392, 294], "id": 180, "thread": "build-17"}, {"duration": 4, "stepId": "io.quarkus.resteasy.reactive.jackson.deployment.processor.ResteasyReactiveJacksonProcessor#reflection", "started": "12:12:54.171", "dependents": [528], "id": 49, "thread": "build-31"}, {"duration": 4, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmCdiProcessor#validatePersistenceUnitExtensions", "started": "12:12:55.482", "dependents": [481], "id": 468, "thread": "build-48"}, {"duration": 4, "stepId": "io.quarkus.arc.deployment.staticmethods.InterceptedStaticMethodsProcessor#processInterceptedStaticMethods", "started": "12:12:55.212", "dependents": [529, 497, 528, 496, 495], "id": 439, "thread": "build-18"}, {"duration": 4, "stepId": "io.quarkus.deployment.steps.ApplicationInfoBuildStep#create", "started": "12:12:54.259", "dependents": [532], "id": 183, "thread": "build-58"}, {"duration": 3, "stepId": "io.quarkus.resteasy.reactive.jackson.deployment.processor.ResteasyReactiveJacksonProcessor#handleJsonAnnotations", "started": "12:12:56.295", "dependents": [507, 528, 532], "id": 506, "thread": "build-123"}, {"duration": 3, "stepId": "io.quarkus.arc.deployment.ArcProcessor#loggerProducer", "started": "12:12:54.184", "dependents": [421, 406], "id": 74, "thread": "build-27"}, {"duration": 3, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmCdiProcessor#registerBeans", "started": "12:12:54.885", "dependents": [421, 406, 469, 461], "id": 399, "thread": "build-64"}, {"duration": 3, "stepId": "io.quarkus.resteasy.reactive.common.deployment.JaxrsMethodsProcessor#jaxrsMethods", "started": "12:12:54.224", "dependents": [320], "id": 145, "thread": "build-71"}, {"duration": 3, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveScanningProcessor#asyncSupport", "started": "12:12:54.183", "dependents": [500], "id": 73, "thread": "build-42"}, {"duration": 3, "stepId": "io.quarkus.arc.deployment.AutoAddScopeProcessor#annotationTransformer", "started": "12:12:54.929", "dependents": [421, 469, 461], "id": 414, "thread": "build-64"}, {"duration": 3, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#includeArchivesHostingEntityPackagesInIndex", "started": "12:12:54.259", "dependents": [312], "id": 176, "thread": "build-66"}, {"duration": 3, "stepId": "io.quarkus.hibernate.orm.deployment.dev.HibernateOrmDevServicesProcessor#devServicesAutoGenerateByDefault", "started": "12:12:54.885", "dependents": [400], "id": 398, "thread": "build-63"}, {"duration": 3, "stepId": "io.quarkus.arc.deployment.ArcProcessor#notifyBeanContainerListeners", "started": "12:12:55.927", "dependents": [532, 484], "id": 483, "thread": "build-48"}, {"duration": 3, "stepId": "io.quarkus.devui.deployment.menu.BuildMetricsProcessor#createBuildMetricsPages", "started": "12:12:54.162", "dependents": [480], "id": 23, "thread": "build-19"}, {"duration": 3, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveScanningProcessor#scanForInterceptors", "started": "12:12:54.776", "dependents": [380], "id": 366, "thread": "build-56"}, {"duration": 3, "stepId": "io.quarkus.smallrye.reactivemessaging.deployment.devui.ReactiveMessagingDevUIProcessor#collectInjectionInfo", "started": "12:12:55.203", "dependents": [532], "id": 428, "thread": "build-55"}, {"duration": 3, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveVertxWebSocketIntegrationProcessor#scanner", "started": "12:12:54.212", "dependents": [500], "id": 128, "thread": "build-40"}, {"duration": 3, "stepId": "io.quarkus.devui.deployment.BuildTimeContentProcessor#gatherMvnpmJars", "started": "12:12:54.264", "dependents": [503, 513], "id": 187, "thread": "build-43"}, {"duration": 3, "stepId": "io.quarkus.arc.deployment.ConfigBuildStep#registerConfigClasses", "started": "12:12:55.487", "dependents": [532], "id": 470, "thread": "build-26"}, {"duration": 3, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ObservabilityProcessor#preAuthFailureFilter", "started": "12:12:56.640", "dependents": [532, 518, 523], "id": 515, "thread": "build-163"}, {"duration": 3, "stepId": "io.quarkus.smallrye.context.deployment.SmallRyeContextPropagationProcessor#createSynthBeansForConfiguredInjectionPoints", "started": "12:12:55.203", "dependents": [454, 532, 449, 450], "id": 427, "thread": "build-61"}, {"duration": 3, "stepId": "io.quarkus.deployment.steps.ShutdownListenerBuildStep#setupShutdown", "started": "12:12:56.869", "dependents": [532], "id": 525, "thread": "build-102"}, {"duration": 3, "stepId": "io.quarkus.agroal.deployment.AgroalProcessor#generateDataSourceBeans", "started": "12:12:54.872", "dependents": [454, 532, 488, 456, 449, 403, 393, 450, 394], "id": 392, "thread": "build-2"}, {"duration": 3, "stepId": "io.quarkus.arc.deployment.StartupBuildSteps#registerStartupObservers", "started": "12:12:55.327", "dependents": [461], "id": 460, "thread": "build-29"}, {"duration": 3, "stepId": "io.quarkus.resteasy.reactive.jackson.deployment.processor.ResteasyReactiveJacksonProcessor#customExceptionMappers", "started": "12:12:54.220", "dependents": [379], "id": 134, "thread": "build-33"}, {"duration": 3, "stepId": "io.quarkus.arc.deployment.ConfigBuildStep#validateConfigPropertiesInjectionPoints", "started": "12:12:55.482", "dependents": [470], "id": 467, "thread": "build-26"}, {"duration": 3, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#additionalReflection", "started": "12:12:56.303", "dependents": [528], "id": 509, "thread": "build-6"}, {"duration": 3, "stepId": "io.quarkus.arc.deployment.devui.ArcDevUIProcessor#pages", "started": "12:12:55.560", "dependents": [476, 477], "id": 475, "thread": "build-56"}, {"duration": 3, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#collectInterceptedMethods", "started": "12:12:54.764", "dependents": [363, 352], "id": 351, "thread": "build-64"}, {"duration": 3, "stepId": "io.quarkus.resteasy.reactive.common.deployment.ResteasyReactiveCommonProcessor#scanForIOInterceptors", "started": "12:12:54.776", "dependents": [380], "id": 367, "thread": "build-37"}, {"duration": 3, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#registerCustomExceptionMappers", "started": "12:12:54.183", "dependents": [379], "id": 69, "thread": "build-41"}, {"duration": 2, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveDevModeProcessor#openCommand", "started": "12:12:56.188", "dependents": [502], "id": 501, "thread": "build-116"}, {"duration": 2, "stepId": "io.quarkus.hibernate.orm.panache.common.deployment.PanacheJpaCommonResourceProcessor#lookupNamedQueries_5a86a91ed8ef1aa483288c8239df231983eeb766", "started": "12:12:54.866", "dependents": [389], "id": 388, "thread": "build-61"}, {"duration": 2, "stepId": "io.quarkus.hibernate.orm.deployment.ResteasyReactiveServerIntegrationProcessor#unwrappedExceptions", "started": "12:12:54.180", "dependents": [381], "id": 59, "thread": "build-5"}, {"duration": 2, "stepId": "io.quarkus.arc.deployment.HotDeploymentConfigBuildStep#startup", "started": "12:12:54.161", "dependents": [40], "id": 20, "thread": "build-4"}, {"duration": 2, "stepId": "io.quarkus.arc.deployment.ShutdownBuildSteps#registerShutdownObservers", "started": "12:12:55.327", "dependents": [461], "id": 459, "thread": "build-18"}, {"duration": 2, "stepId": "io.quarkus.rest.client.reactive.deployment.RestClientReactiveProcessor#activateSslNativeSupport", "started": "12:12:54.237", "dependents": [294], "id": 150, "thread": "build-63"}, {"duration": 2, "stepId": "io.quarkus.scheduler.deployment.SchedulerMethodsProcessor#schedulerMethods", "started": "12:12:54.161", "dependents": [320], "id": 19, "thread": "build-11"}, {"duration": 2, "stepId": "io.quarkus.vertx.deployment.EventConsumerMethodsProcessor#eventConsumerMethods", "started": "12:12:54.161", "dependents": [320], "id": 18, "thread": "build-9"}, {"duration": 2, "stepId": "io.quarkus.rest.client.reactive.deployment.devconsole.RestClientReactiveDevUIProcessor#beans", "started": "12:12:54.158", "dependents": [421, 406], "id": 9, "thread": "build-18"}, {"duration": 2, "stepId": "io.quarkus.devui.deployment.menu.ContinuousTestingProcessor#continuousTestingState", "started": "12:12:55.933", "dependents": [532], "id": 489, "thread": "build-48"}, {"duration": 2, "stepId": "io.quarkus.arc.deployment.AutoProducerMethodsProcessor#annotationTransformer", "started": "12:12:54.927", "dependents": [421], "id": 411, "thread": "build-26"}, {"duration": 2, "stepId": "io.quarkus.stork.deployment.SmallRyeStorkProcessor#initializeStork", "started": "12:12:55.319", "dependents": [532], "id": 457, "thread": "build-29"}, {"duration": 2, "stepId": "io.quarkus.arc.deployment.StartupBuildSteps#unremovableBeans", "started": "12:12:54.159", "dependents": [469, 461], "id": 14, "thread": "build-19"}, {"duration": 2, "stepId": "io.quarkus.vertx.http.deployment.StaticResourcesProcessor#runtimeInit", "started": "12:12:55.933", "dependents": [532, 523], "id": 487, "thread": "build-110"}, {"duration": 2, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#shouldNotRemoveHttpServerOptionsCustomizers", "started": "12:12:54.172", "dependents": [469, 461], "id": 46, "thread": "build-32"}, {"duration": 2, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#startPersistenceUnits", "started": "12:12:55.933", "dependents": [524, 526, 532], "id": 488, "thread": "build-45"}, {"duration": 2, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#registerSafeDuplicatedContextInterceptor", "started": "12:12:54.172", "dependents": [421, 406], "id": 48, "thread": "build-33"}, {"duration": 2, "stepId": "io.quarkus.arc.deployment.ArcProcessor#launchMode", "started": "12:12:54.212", "dependents": [421, 406], "id": 119, "thread": "build-58"}, {"duration": 2, "stepId": "io.quarkus.arc.deployment.ObserverValidationProcessor#validateApplicationObserver", "started": "12:12:55.482", "dependents": [481], "id": 466, "thread": "build-63"}, {"duration": 2, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#build_d182d2fe7ae008890806ec353e99fa052582ee2d", "started": "12:12:54.885", "dependents": [497, 532], "id": 397, "thread": "build-26"}, {"duration": 2, "stepId": "io.quarkus.narayana.jta.deployment.NarayanaJtaProcessor#registerScope", "started": "12:12:54.207", "dependents": [118], "id": 110, "thread": "build-40"}, {"duration": 2, "stepId": "io.quarkus.redis.deployment.client.RedisDatasourceProcessor#makeCodecsUnremovable", "started": "12:12:54.737", "dependents": [421, 406], "id": 330, "thread": "build-9"}, {"duration": 2, "stepId": "io.quarkus.arc.deployment.ArcProcessor#signalBeanContainerReady", "started": "12:12:55.931", "dependents": [511, 510, 514, 523, 489, 532, 526, 488, 490, 486, 500, 487, 485, 491], "id": 484, "thread": "build-149"}, {"duration": 1, "stepId": "io.quarkus.arc.deployment.BuildTimeEnabledProcessor#findEnablementStereotypes", "started": "12:12:54.737", "dependents": [343, 341, 339, 342], "id": 329, "thread": "build-4"}, {"duration": 1, "stepId": "io.quarkus.arc.deployment.ConfigBuildStep#vetoMPConfigProperties", "started": "12:12:54.194", "dependents": [421], "id": 87, "thread": "build-21"}, {"duration": 1, "stepId": "io.quarkus.smallrye.context.deployment.SmallRyeContextPropagationProcessor#registerBean", "started": "12:12:54.209", "dependents": [421, 406], "id": 113, "thread": "build-58"}, {"duration": 1, "stepId": "io.quarkus.credentials.CredentialsProcessor#unremoveable", "started": "12:12:54.185", "dependents": [469, 461], "id": 72, "thread": "build-43"}, {"duration": 1, "stepId": "io.quarkus.devui.deployment.BuildTimeContentProcessor#createKnownInternalImportMap", "started": "12:12:54.274", "dependents": [503], "id": 205, "thread": "build-9"}, {"duration": 1, "stepId": "io.quarkus.arc.deployment.init.InitializationTaskProcessor#startApplicationInitializer", "started": "12:12:55.319", "dependents": [532], "id": 455, "thread": "build-47"}, {"duration": 1, "stepId": "io.quarkus.narayana.jta.deployment.NarayanaJtaProcessor#transactionContext", "started": "12:12:54.973", "dependents": [424], "id": 422, "thread": "build-2"}, {"duration": 1, "stepId": "io.quarkus.arc.deployment.AutoInjectFieldProcessor#autoInjectQualifiers", "started": "12:12:54.927", "dependents": [414, 412], "id": 410, "thread": "build-55"}, {"duration": 1, "stepId": "io.quarkus.smallrye.reactivemessaging.deployment.WiringProcessor#autoConfigureConnectorForOrphansAndProduceManagedChannels", "started": "12:12:55.218", "dependents": [481, 451, 448, 472], "id": 443, "thread": "build-56"}, {"duration": 1, "stepId": "io.quarkus.mutiny.deployment.MutinyProcessor#runtimeInit", "started": "12:12:54.393", "dependents": [532], "id": 284, "thread": "build-61"}, {"duration": 1, "stepId": "io.quarkus.deployment.steps.DevServicesConfigBuildStep#setup", "started": "12:12:54.888", "dependents": [524, 526, 401, 472, 453, 452, 404, 423], "id": 400, "thread": "build-26"}, {"duration": 1, "stepId": "io.quarkus.devui.deployment.BuildTimeContentProcessor#loadAllBuildTimeTemplates", "started": "12:12:56.246", "dependents": [513], "id": 504, "thread": "build-123"}, {"duration": 1, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#responseStatusSupport", "started": "12:12:54.196", "dependents": [500], "id": 93, "thread": "build-41"}, {"duration": 1, "stepId": "io.quarkus.netty.deployment.NettyProcessor#cleanupUnsafeLog", "started": "12:12:54.199", "dependents": [382, 160], "id": 95, "thread": "build-41"}, {"duration": 1, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#config", "started": "12:12:54.170", "dependents": [472], "id": 27, "thread": "build-12"}, {"duration": 1, "stepId": "io.quarkus.hibernate.orm.panache.common.deployment.PanacheJpaCommonResourceProcessor#buildNamedQueryMap", "started": "12:12:54.868", "dependents": [532], "id": 389, "thread": "build-26"}, {"duration": 1, "stepId": "io.quarkus.smallrye.reactivemessaging.deployment.devui.ReactiveMessagingDevUIProcessor#createJsonRPCServiceForCache", "started": "12:12:54.207", "dependents": [286, 364], "id": 108, "thread": "build-58"}, {"duration": 1, "stepId": "io.quarkus.hibernate.orm.panache.deployment.PanacheHibernateResourceProcessor#validate", "started": "12:12:55.482", "dependents": [481], "id": 465, "thread": "build-29"}, {"duration": 1, "stepId": "io.quarkus.deployment.steps.DevServicesConfigBuildStep#deprecated", "started": "12:12:54.193", "dependents": [400], "id": 89, "thread": "build-47"}, {"duration": 1, "stepId": "io.quarkus.jdbc.postgresql.deployment.PostgreSQLJDBCReflections#build", "started": "12:12:54.219", "dependents": [528], "id": 132, "thread": "build-69"}, {"duration": 1, "stepId": "io.quarkus.narayana.jta.deployment.NarayanaJtaProcessor#startRecoveryService", "started": "12:12:55.319", "dependents": [532], "id": 456, "thread": "build-18"}, {"duration": 1, "stepId": "io.quarkus.devservices.postgresql.deployment.PostgresqlDevServicesProcessor#psqlCommand", "started": "12:12:54.889", "dependents": [502], "id": 401, "thread": "build-64"}, {"duration": 1, "stepId": "io.quarkus.deployment.execannotations.ExecutionModelAnnotationsProcessor#devuiJsonRpcServices", "started": "12:12:54.180", "dependents": [320], "id": 60, "thread": "build-32"}, {"duration": 1, "stepId": "io.quarkus.hibernate.orm.panache.deployment.PanacheHibernateResourceProcessor#recordEntityToPersistenceUnit", "started": "12:12:56.185", "dependents": [532], "id": 498, "thread": "build-30"}, {"duration": 1, "stepId": "io.quarkus.rest.client.reactive.deployment.devconsole.RestClientReactiveDevUIProcessor#create", "started": "12:12:54.184", "dependents": [476, 477], "id": 68, "thread": "build-32"}, {"duration": 1, "stepId": "io.quarkus.arc.deployment.staticmethods.InterceptedStaticMethodsProcessor#callInitializer", "started": "12:12:55.933", "dependents": [532], "id": 485, "thread": "build-144"}, {"duration": 1, "stepId": "io.quarkus.smallrye.reactivemessaging.deployment.SmallRyeReactiveMessagingProcessor#producesCoroutineConfiguration", "started": "12:12:54.194", "dependents": [96], "id": 86, "thread": "build-48"}, {"duration": 1, "stepId": "io.quarkus.smallrye.reactivemessaging.rabbitmq.deployment.devui.RabbitDevUIProcessor#registerJsonRpcBackend", "started": "12:12:54.203", "dependents": [286, 364], "id": 100, "thread": "build-56"}, {"duration": 1, "stepId": "io.quarkus.arc.deployment.ArcProcessor#exposeCustomScopeNames", "started": "12:12:54.213", "dependents": [414, 411, 421, 406, 159, 408, 430, 171, 361, 240], "id": 118, "thread": "build-64"}, {"duration": 1, "stepId": "io.quarkus.deployment.pkg.steps.NativeImageBuildStep#ignoreBuildPropertyChanges", "started": "12:12:54.184", "dependents": [279], "id": 70, "thread": "build-37"}, {"duration": 1, "stepId": "io.quarkus.arc.deployment.HotDeploymentConfigBuildStep#configFile", "started": "12:12:54.182", "dependents": [446], "id": 62, "thread": "build-40"}, {"duration": 1, "stepId": "io.quarkus.arc.deployment.ConfigBuildStep#validateConfigValues", "started": "12:12:55.933", "dependents": [528, 532], "id": 486, "thread": "build-109"}, {"duration": 1, "stepId": "io.quarkus.rest.client.reactive.deployment.RestClientReactiveProcessor#registerQueryParamStyleForConfig", "started": "12:12:54.179", "dependents": [298], "id": 57, "thread": "build-38"}, {"duration": 1, "stepId": "io.quarkus.smallrye.reactivemessaging.deployment.devui.ReactiveMessagingDevUIProcessor#create", "started": "12:12:54.180", "dependents": [476, 477], "id": 61, "thread": "build-17"}, {"duration": 1, "stepId": "io.quarkus.arc.deployment.ShutdownBuildSteps#unremovableBeans", "started": "12:12:54.196", "dependents": [469, 461], "id": 92, "thread": "build-21"}, {"duration": 0, "stepId": "io.quarkus.deployment.JniProcessor#setupJni", "started": "12:12:54.264", "dependents": [294], "id": 184, "thread": "build-72"}, {"duration": 0, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#produceEagerSecurityInterceptorStorage", "started": "12:12:54.767", "dependents": [454, 532, 449, 450], "id": 352, "thread": "build-52"}, {"duration": 0, "stepId": "io.quarkus.deployment.console.ConsoleProcessor#installCliCommands", "started": "12:12:56.190", "dependents": [524, 526], "id": 502, "thread": "build-123"}, {"duration": 0, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#cleanupVertxWarnings", "started": "12:12:54.186", "dependents": [382, 160], "id": 71, "thread": "build-40"}, {"duration": 0, "stepId": "io.quarkus.deployment.steps.CurateOutcomeBuildStep#removeResources", "started": "12:12:54.264", "dependents": [529], "id": 182, "thread": "build-70"}, {"duration": 0, "stepId": "io.quarkus.devui.deployment.BuildTimeContentProcessor#mapDeploymentMethods", "started": "12:12:54.391", "dependents": [364, 491], "id": 281, "thread": "build-37"}, {"duration": 0, "stepId": "io.quarkus.devui.deployment.menu.EndpointsProcessor#createEndpointsPage", "started": "12:12:54.274", "dependents": [480], "id": 203, "thread": "build-45"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#candidatesForFieldAccess", "started": "12:12:54.866", "dependents": [390], "id": 387, "thread": "build-26"}, {"duration": 0, "stepId": "io.quarkus.deployment.index.ApplicationArchiveBuildStep#addConfiguredIndexedDependencies", "started": "12:12:54.259", "dependents": [312], "id": 174, "thread": "build-70"}, {"duration": 0, "stepId": "io.quarkus.rest.client.reactive.deployment.RestClientReactiveProcessor#handleSseEventFilter", "started": "12:12:54.927", "dependents": [528], "id": 407, "thread": "build-37"}, {"duration": 0, "stepId": "io.quarkus.deployment.execannotations.ExecutionModelAnnotationsProcessor#check", "started": "12:12:54.737", "dependents": [], "id": 320, "thread": "build-6"}, {"duration": 0, "stepId": "io.quarkus.arc.deployment.ArcProcessor#quarkusApplication", "started": "12:12:54.737", "dependents": [421, 406], "id": 324, "thread": "build-61"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#unremovableBeans", "started": "12:12:54.767", "dependents": [469, 461], "id": 354, "thread": "build-68"}, {"duration": 0, "stepId": "io.quarkus.deployment.logging.LoggingResourceProcessor#setProperty", "started": "12:12:54.213", "dependents": [532], "id": 117, "thread": "build-65"}, {"duration": 0, "stepId": "io.quarkus.rest.client.reactive.deployment.RestClientReactiveProcessor#registerProviderBeans", "started": "12:12:54.737", "dependents": [421, 406], "id": 323, "thread": "build-68"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveCDIProcessor#perClassExceptionMapperSupport", "started": "12:12:54.767", "dependents": [421], "id": 355, "thread": "build-14"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#registerSecurityInterceptors", "started": "12:12:54.285", "dependents": [421, 406], "id": 216, "thread": "build-7"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#multitenancy", "started": "12:12:54.885", "dependents": [454, 532, 469, 449, 461, 450], "id": 395, "thread": "build-55"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.jackson.deployment.processor.ResteasyReactiveJacksonProcessor#jacksonRegistered", "started": "12:12:54.161", "dependents": [133], "id": 13, "thread": "build-20"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#addAllWriteableMarker", "started": "12:12:56.303", "dependents": [529], "id": 508, "thread": "build-8"}, {"duration": 0, "stepId": "io.quarkus.deployment.logging.LoggingWithPanacheProcessor#process", "started": "12:12:54.737", "dependents": [529], "id": 322, "thread": "build-20"}, {"duration": 0, "stepId": "io.quarkus.deployment.console.ConsoleProcessor#missingDevUIMessageHandler", "started": "12:12:54.519", "dependents": [524, 526], "id": 307, "thread": "build-55"}, {"duration": 0, "stepId": "io.quarkus.arc.deployment.ConfigBuildStep#validateConfigInjectionPoints", "started": "12:12:55.482", "dependents": [486], "id": 464, "thread": "build-61"}, {"duration": 0, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#preventLoggerContention", "started": "12:12:54.161", "dependents": [192], "id": 16, "thread": "build-22"}, {"duration": 0, "stepId": "io.quarkus.deployment.ForkJoinPoolProcessor#setProperty", "started": "12:12:54.159", "dependents": [532], "id": 7, "thread": "build-20"}, {"duration": 0, "stepId": "io.quarkus.scheduler.deployment.devui.SchedulerDevUIProcessor#page", "started": "12:12:55.216", "dependents": [476, 477], "id": 438, "thread": "build-6"}, {"duration": 0, "stepId": "io.quarkus.scheduler.deployment.SchedulerProcessor#produceCoroutineScope", "started": "12:12:54.158", "dependents": [421, 406], "id": 1, "thread": "build-17"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.jackson.deployment.processor.ResteasyReactiveJacksonProcessor#initializeRolesAllowedConfigExp", "started": "12:12:55.482", "dependents": [532], "id": 463, "thread": "build-2"}, {"duration": 0, "stepId": "io.quarkus.vertx.deployment.VertxProcessor#featureAndCapability", "started": "12:12:54.214", "dependents": [532, 215], "id": 125, "thread": "build-65"}, {"duration": 0, "stepId": "io.quarkus.vertx.http.deployment.HttpSecurityProcessor#initMtlsClientAuth", "started": "12:12:54.259", "dependents": [421, 406], "id": 175, "thread": "build-35"}, {"duration": 0, "stepId": "io.quarkus.arc.deployment.ArcProcessor#feature", "started": "12:12:54.173", "dependents": [532], "id": 43, "thread": "build-29"}, {"duration": 0, "stepId": "io.quarkus.deployment.recording.AnnotationProxyBuildStep#build", "started": "12:12:54.536", "dependents": [442, 447], "id": 311, "thread": "build-24"}, {"duration": 0, "stepId": "io.quarkus.arc.deployment.ArcProcessor#unremovableAsyncObserverExceptionHandlers", "started": "12:12:54.176", "dependents": [469, 461], "id": 50, "thread": "build-36"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#enrollBeanValidationTypeSafeActivatorForReflection", "started": "12:12:54.285", "dependents": [528], "id": 219, "thread": "build-45"}, {"duration": 0, "stepId": "io.quarkus.arc.deployment.LookupConditionsProcessor#suppressConditionsGenerators", "started": "12:12:54.927", "dependents": [421], "id": 409, "thread": "build-64"}, {"duration": 0, "stepId": "io.quarkus.arc.deployment.ExecutorServiceProcessor#executorServiceBean", "started": "12:12:54.393", "dependents": [454, 449, 450], "id": 283, "thread": "build-37"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#buildSetup", "started": "12:12:54.209", "dependents": [532], "id": 112, "thread": "build-58"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#registerHibernateOrmMetadataForCoreDialects", "started": "12:12:54.183", "dependents": [394], "id": 66, "thread": "build-32"}, {"duration": 0, "stepId": "io.quarkus.deployment.ConstructorPropertiesProcessor#build", "started": "12:12:54.737", "dependents": [528], "id": 321, "thread": "build-71"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.panache.deployment.PanacheHibernateResourceProcessor#featureBuildItem", "started": "12:12:54.164", "dependents": [532], "id": 22, "thread": "build-20"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveCDIProcessor#pathInterfaceImpls", "started": "12:12:54.767", "dependents": [421, 406], "id": 357, "thread": "build-35"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#configureHandlers", "started": "12:12:56.652", "dependents": [532], "id": 517, "thread": "build-163"}, {"duration": 0, "stepId": "io.quarkus.devui.deployment.menu.ConfigurationProcessor#createConfigurationPages", "started": "12:12:55.284", "dependents": [480], "id": 452, "thread": "build-29"}, {"duration": 0, "stepId": "io.quarkus.deployment.ExtensionLoader#booleanSupplierFactory", "started": "12:12:54.214", "dependents": [208], "id": 122, "thread": "build-66"}, {"duration": 0, "stepId": "io.quarkus.devui.deployment.menu.ContinuousTestingProcessor#createContinuousTestingPages", "started": "12:12:54.189", "dependents": [480], "id": 76, "thread": "build-43"}, {"duration": 0, "stepId": "io.quarkus.redis.deployment.client.RedisClientProcessor#feature", "started": "12:12:54.193", "dependents": [532], "id": 83, "thread": "build-3"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateLogFilterBuildStep#setupLogFilters", "started": "12:12:54.205", "dependents": [382, 160], "id": 103, "thread": "build-56"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.jackson.deployment.processor.ResteasyReactiveJacksonProcessor#feature", "started": "12:12:54.193", "dependents": [532], "id": 84, "thread": "build-21"}, {"duration": 0, "stepId": "io.quarkus.config.yaml.deployment.ConfigYamlProcessor#feature", "started": "12:12:54.214", "dependents": [532], "id": 121, "thread": "build-65"}, {"duration": 0, "stepId": "io.quarkus.smallrye.reactivemessaging.deployment.SmallRyeReactiveMessagingProcessor#feature", "started": "12:12:54.180", "dependents": [532], "id": 58, "thread": "build-40"}, {"duration": 0, "stepId": "io.quarkus.rest.client.reactive.deployment.RestClientReactiveProcessor#registerProvidersInstances", "started": "12:12:54.737", "dependents": [377], "id": 325, "thread": "build-35"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateUserTypeProcessor#build", "started": "12:12:54.737", "dependents": [528], "id": 326, "thread": "build-51"}, {"duration": 0, "stepId": "io.quarkus.jdbc.postgresql.deployment.JDBCPostgreSQLProcessor#feature", "started": "12:12:54.172", "dependents": [532], "id": 39, "thread": "build-7"}, {"duration": 0, "stepId": "io.quarkus.deployment.steps.ConfigGenerationBuildStep#suppressNonRuntimeConfigChanged", "started": "12:12:54.201", "dependents": [279], "id": 97, "thread": "build-41"}, {"duration": 0, "stepId": "io.quarkus.devui.deployment.menu.ExtensionsProcessor#createExtensionsPages", "started": "12:12:55.853", "dependents": [480], "id": 478, "thread": "build-116"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.common.deployment.ResteasyReactiveCommonProcessor#searchForProviders", "started": "12:12:54.285", "dependents": [312], "id": 222, "thread": "build-37"}, {"duration": 0, "stepId": "io.quarkus.smallrye.reactivemessaging.rabbitmq.deployment.devui.RabbitDevUIProcessor#beans", "started": "12:12:54.205", "dependents": [421, 406], "id": 101, "thread": "build-58"}, {"duration": 0, "stepId": "io.quarkus.rest.client.reactive.jackson.deployment.RestClientReactiveJacksonProcessor#feature", "started": "12:12:54.195", "dependents": [532], "id": 88, "thread": "build-16"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#handleClassLevelExceptionMappers", "started": "12:12:54.767", "dependents": [528, 500], "id": 356, "thread": "build-19"}, {"duration": 0, "stepId": "io.quarkus.smallrye.reactivemessaging.rabbitmq.deployment.SmallRyeReactiveMessagingRabbitMQProcessor#feature", "started": "12:12:54.214", "dependents": [532], "id": 126, "thread": "build-67"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#defineTypeOfImpliedPU", "started": "12:12:54.876", "dependents": [396, 403, 394], "id": 393, "thread": "build-63"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmCdiProcessor#convertJpaResourceAnnotationsToQualifier", "started": "12:12:54.885", "dependents": [421], "id": 396, "thread": "build-18"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.common.deployment.ResteasyReactiveCommonProcessor#resourceIndex", "started": "12:12:54.737", "dependents": [406, 505, 353], "id": 327, "thread": "build-18"}, {"duration": 0, "stepId": "io.quarkus.arc.deployment.ArcProcessor#validateAsyncObserverExceptionHandlers", "started": "12:12:55.482", "dependents": [481], "id": 462, "thread": "build-37"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#setMinimalNettyMaxOrderSize", "started": "12:12:54.164", "dependents": [196, 204], "id": 21, "thread": "build-9"}, {"duration": 0, "stepId": "io.quarkus.rest.client.reactive.deployment.RestClientReactiveProcessor#registerHeaderFactoryBeans", "started": "12:12:54.736", "dependents": [421, 406], "id": 317, "thread": "build-9"}, {"duration": 0, "stepId": "io.quarkus.deployment.steps.PreloadClassesBuildStep#registerPreInitClasses", "started": "12:12:54.178", "dependents": [], "id": 51, "thread": "build-36"}, {"duration": 0, "stepId": "io.quarkus.stork.deployment.SmallRyeStorkProcessor#checkThatTheKubernetesExtensionIsUsedWhenKubernetesServiceDiscoveryInOnTheClasspath", "started": "12:12:54.285", "dependents": [457], "id": 220, "thread": "build-31"}, {"duration": 0, "stepId": "io.quarkus.devui.deployment.DevUIProcessor#createAllRoutes", "started": "12:12:55.975", "dependents": [513], "id": 493, "thread": "build-30"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#contributePersistenceXmlToJpaModel", "started": "12:12:54.493", "dependents": [386], "id": 303, "thread": "build-9"}, {"duration": 0, "stepId": "io.quarkus.deployment.steps.BannerProcessor#watchBannerChanges", "started": "12:12:54.259", "dependents": [446], "id": 173, "thread": "build-26"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#produceLoggingCategories", "started": "12:12:54.259", "dependents": [192], "id": 172, "thread": "build-64"}, {"duration": 0, "stepId": "io.quarkus.smallrye.openapi.deployment.SmallRyeOpenApiProcessor#contributeClassesToIndex", "started": "12:12:54.184", "dependents": [316], "id": 67, "thread": "build-40"}, {"duration": 0, "stepId": "io.quarkus.deployment.dev.testing.TestTracingProcessor#handle", "started": "12:12:54.171", "dependents": [382, 160], "id": 30, "thread": "build-30"}, {"duration": 0, "stepId": "io.quarkus.arc.deployment.ConfigBuildStep#registerCustomConfigBeanTypes", "started": "12:12:55.203", "dependents": [454, 528, 449, 450], "id": 425, "thread": "build-52"}, {"duration": 0, "stepId": "io.quarkus.arc.deployment.BuildTimeEnabledProcessor#conditionTransformer", "started": "12:12:54.752", "dependents": [421], "id": 345, "thread": "build-68"}, {"duration": 0, "stepId": "io.quarkus.redis.deployment.client.RedisClientProcessor#activateSslNativeSupport", "started": "12:12:54.214", "dependents": [294], "id": 127, "thread": "build-66"}, {"duration": 0, "stepId": "io.quarkus.smallrye.reactivemessaging.deployment.SmallRyeReactiveMessagingProcessor#transformBeanScope", "started": "12:12:54.927", "dependents": [421], "id": 408, "thread": "build-63"}, {"duration": 0, "stepId": "io.quarkus.agroal.deployment.AgroalProcessor#agroal", "started": "12:12:54.220", "dependents": [532], "id": 131, "thread": "build-53"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveCDIProcessor#unremovableContextMethodParams", "started": "12:12:54.767", "dependents": [469, 461], "id": 359, "thread": "build-56"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveProcessor#additionalAsyncTypeMethodScanners", "started": "12:12:54.209", "dependents": [500], "id": 111, "thread": "build-9"}, {"duration": 0, "stepId": "io.quarkus.smallrye.reactivemessaging.deployment.SmallRyeReactiveMessagingProcessor#disableObservation", "started": "12:12:54.161", "dependents": [472], "id": 15, "thread": "build-2"}, {"duration": 0, "stepId": "io.quarkus.vertx.http.deployment.VertxHttpProcessor#notFoundRoutes", "started": "12:12:56.767", "dependents": [522], "id": 520, "thread": "build-75"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveCDIProcessor#subResourcesAsBeans", "started": "12:12:54.767", "dependents": [421, 406, 469, 461], "id": 358, "thread": "build-52"}, {"duration": 0, "stepId": "io.quarkus.devui.deployment.menu.DevServicesProcessor#createDevServicesPages", "started": "12:12:54.895", "dependents": [480], "id": 405, "thread": "build-37"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#checkTransactionsSupport", "started": "12:12:54.285", "dependents": [481], "id": 223, "thread": "build-9"}, {"duration": 0, "stepId": "io.quarkus.jackson.deployment.JacksonProcessor#autoRegisterModules", "started": "12:12:54.737", "dependents": [362], "id": 319, "thread": "build-71"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.panache.deployment.PanacheHibernateResourceProcessor#collectEntityClasses", "started": "12:12:54.737", "dependents": [497], "id": 318, "thread": "build-20"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.dev.HibernateOrmDevUIProcessor#createJsonRPCService", "started": "12:12:54.183", "dependents": [286, 364], "id": 65, "thread": "build-17"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#addPersistenceUnitAnnotationToIndex", "started": "12:12:54.174", "dependents": [316], "id": 47, "thread": "build-16"}, {"duration": 0, "stepId": "io.quarkus.smallrye.reactivemessaging.deployment.WiringProcessor#detectOrphanChannels", "started": "12:12:55.218", "dependents": [443], "id": 441, "thread": "build-18"}, {"duration": 0, "stepId": "io.quarkus.arc.deployment.ConfigBuildStep#registerConfigPropertiesBean", "started": "12:12:55.203", "dependents": [458], "id": 426, "thread": "build-26"}, {"duration": 0, "stepId": "io.quarkus.vertx.core.deployment.VertxCoreProcessor#filterNettyHostsFileParsingWarn", "started": "12:12:54.209", "dependents": [382, 160], "id": 109, "thread": "build-61"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmAlwaysEnabledProcessor#featureBuildItem", "started": "12:12:54.174", "dependents": [532], "id": 45, "thread": "build-7"}, {"duration": 0, "stepId": "io.quarkus.resteasy.reactive.server.deployment.ObservabilityProcessor#methodScanner", "started": "12:12:54.285", "dependents": [500], "id": 218, "thread": "build-59"}, {"duration": 0, "stepId": "io.quarkus.arc.deployment.AutoInjectFieldProcessor#annotationTransformer", "started": "12:12:54.929", "dependents": [421], "id": 412, "thread": "build-63"}, {"duration": 0, "stepId": "io.quarkus.agroal.deployment.AgroalProcessor#adaptOpenTelemetryJdbcInstrumentationForNative", "started": "12:12:54.285", "dependents": [529], "id": 217, "thread": "build-15"}, {"duration": 0, "stepId": "io.quarkus.hibernate.orm.deployment.HibernateOrmProcessor#warnOfSchemaProblems", "started": "12:12:56.868", "dependents": [532], "id": 524, "thread": "build-75"}, {"duration": 0, "stepId": "io.quarkus.deployment.steps.ReflectionDiagnosticProcessor#writeReflectionData", "started": "12:12:56.896", "dependents": [], "id": 528, "thread": "build-141"}, {"duration": 0, "stepId": "io.quarkus.caffeine.deployment.CaffeineProcessor#cacheLoaders", "started": "12:12:54.737", "dependents": [528], "id": 328, "thread": "build-48"}], "started": "2025-06-02T12:12:54.145", "items": [{"count": 1712, "class": "io.quarkus.deployment.builditem.GeneratedClassBuildItem"}, {"count": 1342, "class": "io.quarkus.deployment.builditem.ConfigDescriptionBuildItem"}, {"count": 977, "class": "io.quarkus.deployment.builditem.nativeimage.ReflectiveClassBuildItem"}, {"count": 669, "class": "io.quarkus.deployment.builditem.nativeimage.ReflectiveMethodBuildItem"}, {"count": 422, "class": "io.quarkus.deployment.builditem.BytecodeTransformerBuildItem"}, {"count": 419, "class": "io.quarkus.deployment.builditem.nativeimage.ReflectiveHierarchyBuildItem"}, {"count": 70, "class": "io.quarkus.hibernate.orm.panache.common.deployment.PanacheNamedQueryEntityClassBuildStep"}, {"count": 69, "class": "io.quarkus.arc.deployment.AdditionalBeanBuildItem"}, {"count": 67, "class": "io.quarkus.deployment.builditem.MainBytecodeRecorderBuildItem"}, {"count": 66, "class": "io.quarkus.hibernate.orm.panache.deployment.EntityToPersistenceUnitBuildItem"}, {"count": 58, "class": "io.quarkus.arc.deployment.SyntheticBeanBuildItem"}, {"count": 47, "class": "io.quarkus.deployment.builditem.StaticBytecodeRecorderBuildItem"}, {"count": 45, "class": "io.quarkus.vertx.http.deployment.RouteBuildItem"}, {"count": 38, "class": "io.quarkus.deployment.builditem.nativeimage.ReflectiveFieldBuildItem"}, {"count": 38, "class": "io.quarkus.deployment.builditem.nativeimage.RuntimeInitializedClassBuildItem"}, {"count": 35, "class": "io.quarkus.deployment.builditem.HotDeploymentWatchedFileBuildItem"}, {"count": 29, "class": "io.quarkus.arc.deployment.UnremovableBeanBuildItem"}, {"count": 24, "class": "io.quarkus.deployment.builditem.RunTimeConfigurationDefaultBuildItem"}, {"count": 20, "class": "io.quarkus.deployment.builditem.CapabilityBuildItem"}, {"count": 19, "class": "io.quarkus.deployment.builditem.FeatureBuildItem"}, {"count": 17, "class": "io.quarkus.deployment.logging.LogCleanupFilterBuildItem"}, {"count": 15, "class": "io.quarkus.deployment.builditem.AdditionalIndexedClassesBuildItem"}, {"count": 15, "class": "io.quarkus.deployment.builditem.ConfigClassBuildItem"}, {"count": 13, "class": "io.quarkus.vertx.http.deployment.webjar.WebJarBuildItem"}, {"count": 13, "class": "io.quarkus.arc.deployment.AnnotationsTransformerBuildItem"}, {"count": 12, "class": "io.quarkus.devui.deployment.DevUIWebJarBuildItem"}, {"count": 12, "class": "io.quarkus.devui.deployment.DevUIRoutesBuildItem"}, {"count": 12, "class": "io.quarkus.devui.spi.JsonRPCProvidersBuildItem"}, {"count": 11, "class": "io.quarkus.deployment.builditem.SuppressNonRuntimeConfigChangedWarningBuildItem"}, {"count": 10, "class": "io.quarkus.devui.spi.page.CardPageBuildItem"}, {"count": 10, "class": "io.quarkus.arc.deployment.GeneratedBeanBuildItem"}, {"count": 10, "class": "io.quarkus.deployment.builditem.nativeimage.RuntimeReinitializedClassBuildItem"}, {"count": 10, "class": "io.quarkus.scheduler.deployment.ScheduledBusinessMethodItem"}, {"count": 10, "class": "io.quarkus.resteasy.reactive.spi.MessageBodyWriterBuildItem"}, {"count": 8, "class": "io.quarkus.hibernate.orm.deployment.spi.DatabaseKindDialectBuildItem"}, {"count": 7, "class": "io.quarkus.deployment.builditem.ConsoleCommandBuildItem"}, {"count": 7, "class": "io.quarkus.resteasy.reactive.spi.MessageBodyReaderBuildItem"}, {"count": 7, "class": "io.quarkus.devui.deployment.InternalPageBuildItem"}, {"count": 7, "class": "io.quarkus.resteasy.reactive.spi.ExceptionMapperBuildItem"}, {"count": 7, "class": "io.quarkus.vertx.http.deployment.devmode.NotFoundPageDisplayableEndpointBuildItem"}, {"count": 6, "class": "io.quarkus.deployment.builditem.SystemPropertyBuildItem"}, {"count": 6, "class": "io.quarkus.arc.deployment.BeanDefiningAnnotationBuildItem"}, {"count": 6, "class": "io.quarkus.deployment.builditem.nativeimage.NativeImageSystemPropertyBuildItem"}, {"count": 6, "class": "io.quarkus.resteasy.reactive.server.spi.MethodScannerBuildItem"}, {"count": 6, "class": "io.quarkus.devui.deployment.BuildTimeConstBuildItem"}, {"count": 6, "class": "io.quarkus.arc.deployment.ConfigPropertyBuildItem"}, {"count": 5, "class": "io.quarkus.deployment.builditem.RunTimeConfigBuilderBuildItem"}, {"count": 5, "class": "io.quarkus.deployment.execannotations.ExecutionModelAnnotationsAllowedBuildItem"}, {"count": 5, "class": "io.quarkus.deployment.builditem.nativeimage.NativeImageResourceBuildItem"}, {"count": 4, "class": "io.quarkus.resteasy.reactive.spi.MessageBodyWriterOverrideBuildItem"}, {"count": 4, "class": "io.quarkus.deployment.builditem.ServiceStartBuildItem"}, {"count": 4, "class": "io.quarkus.arc.deployment.BeanRegistrationPhaseBuildItem$BeanConfiguratorBuildItem"}, {"count": 4, "class": "io.quarkus.arc.deployment.AutoAddScopeBuildItem"}, {"count": 4, "class": "io.quarkus.vertx.http.deployment.spi.RouteBuildItem"}, {"count": 4, "class": "io.quarkus.devui.spi.buildtime.BuildTimeActionBuildItem"}, {"count": 4, "class": "io.quarkus.resteasy.reactive.spi.MessageBodyReaderOverrideBuildItem"}, {"count": 4, "class": "io.quarkus.deployment.builditem.ConfigMappingBuildItem"}, {"count": 4, "class": "io.quarkus.deployment.builditem.AdditionalApplicationArchiveMarkerBuildItem"}, {"count": 3, "class": "io.quarkus.vertx.http.deployment.HttpAuthMechanismAnnotationBuildItem"}, {"count": 3, "class": "io.quarkus.deployment.builditem.StaticInitConfigBuilderBuildItem"}, {"count": 3, "class": "io.quarkus.jackson.spi.ClassPathJacksonModuleBuildItem"}, {"count": 3, "class": "io.quarkus.deployment.builditem.ExtensionSslNativeSupportBuildItem"}, {"count": 3, "class": "io.quarkus.vertx.http.deployment.FilterBuildItem"}, {"count": 3, "class": "io.quarkus.resteasy.reactive.server.spi.UnwrappedExceptionBuildItem"}, {"count": 3, "class": "io.quarkus.resteasy.reactive.spi.CustomExceptionMapperBuildItem"}, {"count": 3, "class": "io.quarkus.deployment.builditem.GeneratedResourceBuildItem"}, {"count": 3, "class": "io.quarkus.resteasy.reactive.spi.ContainerRequestFilterBuildItem"}, {"count": 2, "class": "io.quarkus.deployment.builditem.ShutdownListenerBuildItem"}, {"count": 2, "class": "io.quarkus.resteasy.reactive.common.deployment.ResourceInterceptorsContributorBuildItem"}, {"count": 2, "class": "io.quarkus.smallrye.reactivemessaging.deployment.items.ConnectorBuildItem"}, {"count": 2, "class": "io.quarkus.deployment.builditem.ObjectSubstitutionBuildItem"}, {"count": 2, "class": "io.quarkus.devui.spi.buildtime.QuteTemplateBuildItem"}, {"count": 2, "class": "io.quarkus.deployment.builditem.nativeimage.NativeImageConfigBuildItem"}, {"count": 2, "class": "io.quarkus.deployment.builditem.RecordableConstructorBuildItem"}, {"count": 2, "class": "io.quarkus.resteasy.reactive.spi.ContainerResponseFilterBuildItem"}, {"count": 2, "class": "io.quarkus.deployment.builditem.BytecodeRecorderObjectLoaderBuildItem"}, {"count": 2, "class": "io.quarkus.devui.spi.buildtime.StaticContentBuildItem"}, {"count": 2, "class": "io.quarkus.deployment.builditem.LogCategoryBuildItem"}, {"count": 2, "class": "io.quarkus.deployment.dev.testing.TestListenerBuildItem"}, {"count": 2, "class": "io.quarkus.devui.deployment.InternalImportMapBuildItem"}, {"count": 2, "class": "io.quarkus.arc.deployment.AutoInjectAnnotationBuildItem"}, {"count": 1, "class": "io.quarkus.devui.deployment.MvnpmBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.AnnotationProxyBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.BytecodeRecorderConstantDefinitionBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.console.ConsoleInstalledBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.SynthesisFinishedBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.nativeimage.NativeImageResourceBundleBuildItem"}, {"count": 1, "class": "io.quarkus.vertx.core.deployment.EventLoopCountBuildItem"}, {"count": 1, "class": "io.quarkus.vertx.core.deployment.CoreVertxBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.server.deployment.ContextResolversBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.DockerStatusBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.nativeimage.ReflectiveHierarchyIgnoreWarningBuildItem"}, {"count": 1, "class": "io.quarkus.vertx.deployment.LocalCodecSelectorTypesBuildItem"}, {"count": 1, "class": "io.quarkus.vertx.http.deployment.InitialRouterBuildItem"}, {"count": 1, "class": "io.quarkus.panache.common.deployment.HibernateMetamodelForFieldAccessBuildItem"}, {"count": 1, "class": "io.quarkus.smallrye.openapi.deployment.spi.OpenApiDocumentBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.dev.ExceptionNotificationBuildItem"}, {"count": 1, "class": "io.quarkus.swaggerui.deployment.SwaggerUiBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.pkg.builditem.CompiledJavaVersionBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.ValidationPhaseBuildItem"}, {"count": 1, "class": "io.quarkus.jaxrs.client.reactive.deployment.JaxrsClientReactiveEnricherBuildItem"}, {"count": 1, "class": "io.quarkus.netty.deployment.EventLoopSupplierBuildItem"}, {"count": 1, "class": "io.quarkus.smallrye.reactivemessaging.deployment.items.ConnectorManagedChannelBuildItem"}, {"count": 1, "class": "io.quarkus.smallrye.reactivemessaging.deployment.items.MediatorBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.LogFileFormatBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.BooleanSupplierFactoryBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.server.deployment.ParamConverterProvidersBuildItem"}, {"count": 1, "class": "io.quarkus.hibernate.orm.deployment.ImpliedBlockingPersistenceUnitTypeBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.server.spi.HandlerConfigurationProviderBuildItem"}, {"count": 1, "class": "io.quarkus.datasource.deployment.spi.DevServicesDatasourceProviderBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.DevServicesLauncherConfigResultBuildItem"}, {"count": 1, "class": "io.quarkus.smallrye.reactivemessaging.deployment.items.ChannelBuildItem"}, {"count": 1, "class": "io.quarkus.panache.common.deployment.PanacheEntityClassesBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.ThreadFactoryBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.ApplicationIndexBuildItem"}, {"count": 1, "class": "io.quarkus.panache.common.deployment.HibernateModelClassCandidatesForFieldAccessBuildItem"}, {"count": 1, "class": "io.quarkus.agroal.spi.JdbcDriverBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.logging.LoggingSetupBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.ArcContainerBuildItem"}, {"count": 1, "class": "io.quarkus.devui.deployment.JsonRPCRuntimeMethodsBuildItem"}, {"count": 1, "class": "io.quarkus.smallrye.context.deployment.spi.ThreadContextProviderBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.BeanRegistrationPhaseBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.ApplicationClassNameBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.StreamingLogHandlerBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.dev.DisableInstrumentationForIndexPredicateBuildItem"}, {"count": 1, "class": "io.quarkus.hibernate.orm.deployment.spi.AdditionalJpaModelBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.LogConsoleFormatBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.CurrentContextFactoryBuildItem"}, {"count": 1, "class": "io.quarkus.rest.client.reactive.deployment.AnnotationToRegisterIntoClientContextBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.common.deployment.ParameterContainersBuildItem"}, {"count": 1, "class": "io.quarkus.smallrye.openapi.deployment.spi.AddToOpenAPIDefinitionBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.ConfigurationBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.ApplicationClassPredicateBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.common.deployment.ApplicationResultBuildItem"}, {"count": 1, "class": "io.quarkus.agroal.deployment.AggregatedDataSourceBuildTimeConfigBuildItem"}, {"count": 1, "class": "io.quarkus.vertx.http.deployment.BodyHandlerBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.BuildExclusionsBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.LogCategoryMinLevelDefaultsBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.IOThreadDetectorBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.SslNativeConfigBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.CustomScopeBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.common.deployment.ServerDefaultProducesHandlerBuildItem"}, {"count": 1, "class": "io.quarkus.agroal.spi.JdbcDataSourceBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.ide.IdeRunningProcessBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.BeanContainerListenerBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.TransformedClassesBuildItem"}, {"count": 1, "class": "io.quarkus.netty.deployment.EventLoopGroupBuildItem"}, {"count": 1, "class": "io.quarkus.hibernate.orm.deployment.JpaModelIndexBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.devui.ArcBeanInfoBuildItem"}, {"count": 1, "class": "io.quarkus.datasource.deployment.spi.DevServicesDatasourceConfigurationHandlerBuildItem"}, {"count": 1, "class": "io.quarkus.jaxrs.client.reactive.deployment.RestClientDefaultProducesBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.BeanDiscoveryFinishedBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.RunTimeConfigurationProxyBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.ConfigurationTypeBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.common.deployment.ResourceInterceptorsBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.BuildCompatibleExtensionsBuildItem"}, {"count": 1, "class": "io.quarkus.devui.deployment.ThemeVarsBuildItem"}, {"count": 1, "class": "io.quarkus.datasource.deployment.spi.DefaultDataSourceDbKindBuildItem"}, {"count": 1, "class": "io.quarkus.hibernate.orm.deployment.JpaModelPersistenceUnitMappingBuildItem"}, {"count": 1, "class": "io.quarkus.devui.spi.page.FooterPageBuildItem"}, {"count": 1, "class": "io.quarkus.smallrye.context.deployment.ContextPropagationInitializedBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.server.deployment.ExceptionMappersBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.InterceptorResolverBuildItem"}, {"count": 1, "class": "io.quarkus.panache.common.deployment.HibernateEnhancersRegisteredBuildItem"}, {"count": 1, "class": "io.quarkus.datasource.deployment.spi.DevServicesDatasourceResultBuildItem"}, {"count": 1, "class": "io.quarkus.smallrye.reactivemessaging.deployment.SmallRyeReactiveMessagingProcessor$CoroutineConfigurationBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.BeanArchiveIndexBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.spi.ParamConverterBuildItem"}, {"count": 1, "class": "io.quarkus.jackson.spi.JacksonModuleBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.ConsoleFormatterBannerBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.SuppressConditionGeneratorBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.BuildTimeEnabledStereotypesBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.ApplicationArchivesBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.ContextHandlerBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.TransformedAnnotationsBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveResourceMethodEntriesBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.GeneratedFileSystemResourceHandledBuildItem"}, {"count": 1, "class": "io.quarkus.hibernate.orm.deployment.PersistenceProviderSetUpBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.pkg.builditem.OutputTargetBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.PreBeanContainerBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.InjectionPointTransformerBuildItem"}, {"count": 1, "class": "io.quarkus.vertx.http.deployment.webjar.WebJarResultsBuildItem"}, {"count": 1, "class": "io.quarkus.hibernate.orm.deployment.JpaModelPersistenceUnitContributionBuildItem"}, {"count": 1, "class": "io.quarkus.netty.deployment.MinNettyAllocatorMaxOrderBuildItem"}, {"count": 1, "class": "io.quarkus.smallrye.openapi.deployment.OpenApiFilteredIndexViewBuildItem"}, {"count": 1, "class": "io.quarkus.vertx.http.deployment.NonApplicationRootPathBuildItem"}, {"count": 1, "class": "io.quarkus.vertx.http.deployment.VertxWebRouterBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.CombinedIndexBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.jackson.deployment.processor.ResteasyReactiveJacksonProviderDefinedBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.Capabilities"}, {"count": 1, "class": "io.quarkus.devui.deployment.ExtensionsBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.ExecutorBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.server.deployment.SetupEndpointsResultBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveDeploymentInfoBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.ObserverRegistrationPhaseBuildItem"}, {"count": 1, "class": "io.quarkus.jaxrs.client.reactive.deployment.RestClientDefaultConsumesBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.common.deployment.ResourceScanningResultBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.server.deployment.ServerSerialisersBuildItem"}, {"count": 1, "class": "io.quarkus.hibernate.orm.deployment.JpaModelBuildItem"}, {"count": 1, "class": "io.quarkus.vertx.deployment.VertxBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.server.deployment.ResteasyReactiveDeploymentBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.BeanContainerBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.ide.EffectiveIdeBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.common.deployment.JaxRsResourceIndexBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.LogSyslogFormatBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.pkg.builditem.CurateOutcomeBuildItem"}, {"count": 1, "class": "io.quarkus.vertx.http.deployment.HttpRootPathBuildItem"}, {"count": 1, "class": "io.quarkus.devui.deployment.DeploymentMethodBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.steps.CapabilityAggregationStep$CapabilitiesConfiguredInDescriptorsBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.ApplicationStartBuildItem"}, {"count": 1, "class": "io.quarkus.redis.deployment.client.RequestedRedisClientBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.ContextRegistrationPhaseBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.CustomScopeAnnotationsBuildItem"}, {"count": 1, "class": "io.quarkus.hibernate.orm.deployment.PersistenceUnitDescriptorBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.ContextRegistrationPhaseBuildItem$ContextConfiguratorBuildItem"}, {"count": 1, "class": "io.quarkus.resteasy.reactive.server.deployment.BuiltInReaderOverrideBuildItem"}, {"count": 1, "class": "io.quarkus.arc.deployment.CompletedApplicationClassPredicateBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.ApplicationInfoBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.ide.IdeFileBuildItem"}, {"count": 1, "class": "io.quarkus.deployment.builditem.MainClassBuildItem"}], "itemsCount": 6660, "buildTarget": "backend-admin-1.0-SNAPSHOT"}