package tech.wejoy.billiard.dto;

import com.congeer.web.bean.request.PageRequest;
import jakarta.ws.rs.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.jboss.resteasy.reactive.Separator;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class SmyooAccountQueryDto extends PageRequest {

    @QueryParam("tenantId")
    @Separator(",")
    private List<Long> tenantId;

    @QueryParam("phone")
    private String phone;

}
