package tech.wejoy.billiard.dto;

import com.congeer.web.bean.request.PageRequest;
import jakarta.ws.rs.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.jboss.resteasy.reactive.Separator;
import tech.wejoy.billiard.enums.WithdrawStatusEnum;

import java.time.LocalDate;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class WithdrawRecordQueryDto extends PageRequest {

    @QueryParam("tenantId")
    @Separator(",")
    private List<Long> tenantId;

    @QueryParam("status")
    @Separator(",")
    private List<WithdrawStatusEnum> status;

    @QueryParam("startDate")
    private LocalDate startDate;

    @QueryParam("endDate")
    private LocalDate endDate;

}
