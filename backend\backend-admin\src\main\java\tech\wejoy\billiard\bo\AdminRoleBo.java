package tech.wejoy.billiard.bo;

import io.quarkus.runtime.annotations.RegisterForReflection;
import lombok.Data;
import tech.wejoy.billiard.enums.PermissionTypeEnum;

import java.util.List;

@Data
@RegisterForReflection
public class AdminRoleBo {

    private Long id;

    private Long tenantId;

    private String name;

    private String code;

    private String description;

    private PermissionTypeEnum type;

    private List<String> permissions;

}
