package tech.wejoy.billiard.dto;

import jakarta.ws.rs.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import tech.wejoy.billiard.enums.ProfitTypeEnum;

@EqualsAndHashCode(callSuper = true)
@Data
public class ClubQueryDatePeriodDto extends ClubDatePeriodDto {

    @QueryParam("orderNo")
    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "收益类型")
    @QueryParam("type")
    private ProfitTypeEnum type;

}
