package tech.wejoy.billiard.bo;

import lombok.Data;
import org.eclipse.microprofile.openapi.annotations.media.Schema;

import java.math.BigDecimal;

@Data
public class ProfitSimpleBo {

    private Long tenantId;

    private Long clubId;

    @Schema(description = "服务费")
    private BigDecimal fee = BigDecimal.ZERO;

    @Schema(description = "净收入")
    private BigDecimal profit = BigDecimal.ZERO;

    @Schema(description = "总收入")
    private BigDecimal total = BigDecimal.ZERO;

    @Schema(description = "在线收益")
    private BigDecimal online = BigDecimal.ZERO;

    @Schema(description = "美团券")
    private BigDecimal meituan = BigDecimal.ZERO;

    @Schema(description = "抖音券")
    private BigDecimal douyin = BigDecimal.ZERO;

    @Schema(description = "单点卡充值")
    private BigDecimal club = BigDecimal.ZERO;

    @Schema(description = "优惠券")
    private BigDecimal coupon = BigDecimal.ZERO;

    @Schema(description = "现金")
    private BigDecimal cash = BigDecimal.ZERO;

    @Schema(description = "单店卡充值")
    private BigDecimal clubRecharge = BigDecimal.ZERO;

    @Schema(description = "单店卡赠送")
    private BigDecimal clubGift = BigDecimal.ZERO;

    @Schema(description = "单店卡使用")
    private BigDecimal clubUsed = BigDecimal.ZERO;

    @Schema(description = "团购券")
    private BigDecimal ticket = BigDecimal.ZERO;

    @Schema(description = "订单数")
    private Integer orderCount = 0;

}
