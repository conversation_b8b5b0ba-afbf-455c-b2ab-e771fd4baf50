package tech.wejoy.billiard.bo;

import lombok.Data;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import tech.wejoy.billiard.enums.ProfitTypeEnum;

import java.time.LocalDateTime;

@Data
public class ProfitDetailBo {

    @Schema(description = "收益类型")
    private ProfitTypeEnum type;

    @Schema(description = "订单收益")
    private OrderProfitBo order;

    @Schema(description = "单店卡收益")
    private ClubRechargeProfitBo club;
    private ClubRechargeProfitBo clubRecharge;

    private ClubGiftProfitBo clubGift;

    @Schema(description = "完成时间")
    private LocalDateTime endTime;

}
