api-package:
  image: quay.io/quarkus/ubi-quarkus-mandrel-builder-image:jdk-21
  stage: package
  tags:
    - k3s
  only:
    changes:
      - backend-admin/Dockerfile
      - backend-api/src/**/*
      - backend-api/pom.xml
      - backend-common/src/**/*
      - backend-api/.gitlab-ci.yml
    refs:
      - develop
      - master
      - tags
  script:
    - ./mvnw -B org.apache.maven.plugins:maven-dependency-plugin:3.1.2:go-offline -Dmaven.repo.local=.m2/repository --settings=settings.xml
    #    - ./mvnw clean package -Dnative -Dmaven.repo.local=.m2/repository --settings=settings.xml
    - ./mvnw clean package -pl backend-api -am -Dmaven.repo.local=.m2/repository --settings=settings.xml
  #    - mvn clean package -Dmaven.test.skip=true -Dmaven.repo.local=.m2/repository --settings=settings.xml
  cache:
    key: maven-ci-cache
    paths:
      - .m2/repository
  artifacts:
    paths:
      #      - target/*-runner
      - backend-api/target/quarkus-app/**/*
    expire_in: 1h

api-image-branches:
  stage: image
  tags:
    - k3s
  only:
    changes:
      - backend-api/Dockerfile
      - backend-api/src/**/*
      - backend-common/src/**/*
      - backend-api/pom.xml
      - backend-api/.gitlab-ci.yml
    refs:
      - master
      - develop
  image:
    name: moby/buildkit:rootless
    entrypoint: [ "sh", "-c" ]
  dependencies:
    - api-package
  variables:
    BUILDKITD_FLAGS: --oci-worker-no-process-sandbox
  before_script:
    - |
      mkdir ~/.docker
      echo "{\"auths\":{\"***************:20080\":{\"auth\":\"************************\"}}}" > ~/.docker/config.json
      mkdir -p ~/.config/buildkit
      echo -e "insecure-entitlements = [ \"***************:20080\" ]\n[registry.\"***************:20080\"]\n  http = true" > ~/.config/buildkit/buildkitd.toml
  script:
    - |
      buildctl-daemonless.sh \
          build --frontend=dockerfile.v0 \
          --export-cache type=inline \
          --local context=. \
          --opt filename=./backend-api/Dockerfile \
          --local dockerfile=. \
          --output type=image,name=***************:20080/congeer/billiard-backend-api:$CI_COMMIT_REF_NAME,push=true,registry.insecure=true

api-image-tags:
  stage: image
  tags:
    - k3s
  only:
    refs:
      - tags
  image:
    name: moby/buildkit:rootless
    entrypoint: [ "sh", "-c" ]
  dependencies:
    - api-package
  variables:
    BUILDKITD_FLAGS: --oci-worker-no-process-sandbox
  before_script:
    - |
      mkdir ~/.docker
      echo "{\"auths\":{\"***************:20080\":{\"auth\":\"************************\"}}}" > ~/.docker/config.json
      mkdir -p ~/.config/buildkit
      echo -e "insecure-entitlements = [ \"***************:20080\" ]\n[registry.\"***************:20080\"]\n  http = true" > ~/.config/buildkit/buildkitd.toml
  script:
    - |
      buildctl-daemonless.sh \
          build --frontend=dockerfile.v0 \
          --export-cache type=inline \
          --local context=. \
          --opt filename=./backend-api/Dockerfile \
          --local dockerfile=. \
          --output type=image,name=***************:20080/congeer/billiard-backend-api:$CI_COMMIT_TAG,push=true,registry.insecure=true

api-publish:
  stage: publish
  tags:
    - k3s
  only:
    changes:
      - backend-api/Dockerfile
      - backend-api/src/**/*
      - backend-common/src/**/*
      - backend-api/pom.xml
      - backend-api/.gitlab-ci.yml
    refs:
      - master
  dependencies:
    - api-image-branches
  before_script:
    ##
    ## Install ssh-agent if not already installed, it is required by Docker.
    ## (change apt-get to yum if you use an RPM-based image)
    ##
    - 'command -v ssh-agent >/dev/null || ( apt-get update -y && apt-get install openssh-client -y )'

    ##
    ## Run ssh-agent (inside the build environment)
    ##
    - eval $(ssh-agent -s)

    ##
    ## Give the right permissions, otherwise ssh-add will refuse to add files
    ## Add the SSH key stored in SSH_PRIVATE_KEY file type CI/CD variable to the agent store
    ##
    - chmod 400 "$DEV_SSH_KEY"
    - ssh-add "$DEV_SSH_KEY"

    ##
    ## Create the SSH directory and give it the right permissions
    ##
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh

    ##
    ## Optionally, if you will be using any Git commands, set the user name and
    ## and email.
    ##
    # - git config --global user.email "<EMAIL>"
    # - git config --global user.name "User name"
  script:
    - ssh -o StrictHostKeyChecking=no root@$DEV_HOST "sh $API_DEV_SCRIPT"



#branches:
#  stage: image
#  tags:
#    - k3s
#  only:
#    changes:
#      - Dockerfile-Native
#      - src/**/*
#      - pom.xml
#      - settings.xml
#    refs:
#      - master
#      - develop
#  image:
#    name: moby/buildkit:rootless
#    entrypoint: [ "sh", "-c" ]
#  variables:
#    BUILDKITD_FLAGS: --oci-worker-no-process-sandbox
#  before_script:
#    - |
#      mkdir ~/.docker
#      echo "{\"auths\":{\"***************:20080\":{\"auth\":\"************************\"}}}" > ~/.docker/config.json
#  script:
#    - |
#      buildctl-daemonless.sh \
#          build --frontend=dockerfile.v0 \
#          --export-cache type=inline \
#          --local context=. \
#          --local dockerfile=. \
#          --output type=image,name=***************:20080/congeer/billiard-backend-api:$CI_JOB_NAME,push=true,registry.insecure=true
#
#tags:
#  stage: image
#  tags:
#    - k3s
#  only:
#    refs:
#      - tags
#  image:
#    name: moby/buildkit:rootless
#    entrypoint: [ "sh", "-c" ]
#  variables:
#    BUILDKITD_FLAGS: --oci-worker-no-process-sandbox
#  before_script:
#    - |
#      mkdir ~/.docker
#      echo "{\"auths\":{\"***************:20080\":{\"auth\":\"************************\"}}}" > ~/.docker/config.json
#  script:
#    - |
#      buildctl-daemonless.sh \
#          build --frontend=dockerfile.v0 \
#          --export-cache type=inline \
#          --local context=. \
#          --local dockerfile=. \
#          --output type=image,name=***************:20080/congeer/billiard-backend-api:$CI_COMMIT_TAG,push=true,registry.insecure=true
