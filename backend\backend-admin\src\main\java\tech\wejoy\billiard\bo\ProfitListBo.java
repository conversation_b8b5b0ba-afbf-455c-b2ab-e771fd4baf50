package tech.wejoy.billiard.bo;

import com.congeer.core.bean.Page;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.eclipse.microprofile.openapi.annotations.media.Schema;

import java.math.BigDecimal;

@EqualsAndHashCode(callSuper = true)
@Data
public class ProfitListBo extends Page<ProfitDetailBo> {

    @Schema(description = "总净收入")
    private BigDecimal profit = BigDecimal.ZERO;

}
