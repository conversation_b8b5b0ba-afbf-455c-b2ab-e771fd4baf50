quarkus:
  http:
    cors:
      ~: true
      origins: "*"
      headers: "*"
      methods: "*"
      exposed-headers: "*"
      access-control-allow-credentials: true
      access-control-max-age: 24h
    port: 8081
  datasource:
    username: ${DB_USERNAME:postgres}
    password: ${DB_PASSWORD:admin321.}
    jdbc:
      url: jdbc:postgresql://${DB_URL:***************:15678}/${DB_NAME:bdev}
#      url: jdbc:postgresql://${DB_URL:*************:5678}/${DB_NAME:prod}
  redis:
    hosts: ${REDIS_HOSTS:redis://***************:6379}
#    hosts: ${REDIS_HOSTS:redis://*************:6379}

rabbitmq-host: ${RABBITMQ_HOST:***************}
rabbitmq-port: ${RABBITMQ_PORT:6783}
rabbitmq-username: ${RABBITMQ_USER:rabbit}
rabbitmq-password: ${RABBITMQ_PASS:rabbit321.}
rabbitmq-virtual-host: ${RABBITMQ_VHOST:default}

mp:
  messaging:
    incoming:
      club-device:
        connection-count: 4
        automatic-recovery-enabled: true
        routing-keys: club-device-routing
        queue:
          name: club-device
        connector: smallrye-rabbitmq