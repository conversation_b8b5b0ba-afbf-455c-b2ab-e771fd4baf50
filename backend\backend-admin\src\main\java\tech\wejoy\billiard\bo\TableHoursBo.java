package tech.wejoy.billiard.bo;

import lombok.Data;
import org.eclipse.microprofile.openapi.annotations.media.Schema;

import java.math.BigDecimal;

@Data
public class TableHoursBo {

    @Schema(description = "桌台ID")
    private Long id;

    @Schema(description = "桌台名称")
    private String tableName;

    @Schema(description = "开台时长")
    private BigDecimal hours = BigDecimal.ZERO;

    @Schema(description = "开台次数")
    private Integer count = 0;

}
