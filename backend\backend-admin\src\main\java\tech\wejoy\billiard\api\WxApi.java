package tech.wejoy.billiard.api;

import com.congeer.core.exception.BaseException;
import com.congeer.security.quarkus.annotation.Authorized;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.MediaType;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;
import tech.wejoy.billiard.bo.WxLoginBo;
import tech.wejoy.billiard.service.WxBusinessService;

@Path("/wx")
@Tag(name = "微信相关")
@Produces(MediaType.APPLICATION_JSON)
@RequiredArgsConstructor
public class WxApi {

    private final WxBusinessService userService;

    @Path("/login")
    @POST
    @Operation(summary = "微信登录")
    public WxLoginBo login(@QueryParam("code") String code, @QueryParam("bind") String bind, @QueryParam("username") String username, @QueryParam("password") String password) {
        if (StringUtils.isBlank(code)) {
            throw new BaseException("参数异常");
        }
        return userService.login(code, bind, username, password);
    }

    @Path("/phone")
    @POST
    @Operation(summary = "更新手机号")
    @Authorized("business")
    public void updatePhone(@QueryParam("code") String code) {
        if (StringUtils.isBlank(code)) {
            throw new BaseException("参数异常");
        }
        userService.updatePhone(code);
    }

}
