package tech.wejoy.billiard.bo;

import lombok.Data;
import org.eclipse.microprofile.openapi.annotations.media.Schema;

import java.math.BigDecimal;

@Data
public class OperationsStatsBo {

    @Schema(description = "活跃用户")
    private Integer userCount = 0;

    @Schema(description = "订单量")
    private Integer orderCount = 0;

    @Schema(description = "平均开台时长")
    private BigDecimal avgTableHours = BigDecimal.ZERO;

    @Schema(description = "总开台时长")
    private BigDecimal totalTableHours = BigDecimal.ZERO;

    @Schema(description = "会员单量")
    private Integer memberOrderCount = 0;

    @Schema(description = "非会员单量")
    private Integer nonMemberOrderCount = 0;

    @Schema(description = "会员转化率")
    private BigDecimal memberConversionRate = BigDecimal.ZERO;

    @Schema(description = "会员充值率")
    private BigDecimal memberRechargeRate = BigDecimal.ZERO;

    @Schema(description = "单小时均价")
    private BigDecimal perHourPrice = BigDecimal.ZERO;

    @Schema(description = "团购订单量")
    private Integer ticketOrderCount = 0;

    @Schema(description = "新用户数")
    private Integer newUserCount = 0;


}