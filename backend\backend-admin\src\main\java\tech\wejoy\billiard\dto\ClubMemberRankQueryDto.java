package tech.wejoy.billiard.dto;

import com.congeer.web.bean.request.PageRequest;
import jakarta.ws.rs.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import tech.wejoy.billiard.enums.ClubMemberQueryOrderEnum;

@EqualsAndHashCode(callSuper = true)
@Data
public class ClubMemberRankQueryDto extends PageRequest {
    @QueryParam("tenantId")
    @Schema(hidden = true)
    private Long tenantId;
    @QueryParam("clubId")
    private Long clubId;
    @QueryParam("phone")
    private String phone;
    @QueryParam("orderBy")
    private ClubMemberQueryOrderEnum orderBy = ClubMemberQueryOrderEnum.NONE;

}
