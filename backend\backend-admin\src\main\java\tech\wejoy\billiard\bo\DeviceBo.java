package tech.wejoy.billiard.bo;

import lombok.Data;
import tech.wejoy.billiard.enums.DeviceBrandEnum;
import tech.wejoy.billiard.enums.DeviceTypeEnum;
import tech.wejoy.billiard.enums.IsEnum;

import java.util.List;

@Data
public class DeviceBo {

    private Long id;

    private String name;

    private Long tenantId;

    private Long accountId;

    private DeviceBrandEnum brand;

    private DeviceTypeEnum type;

    private IsEnum online;

    private IsEnum open;

    private Boolean linked;

    private Long parentId;

    private String parentName;

    private List<DeviceRelBo> rel;

}
