<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="25db7718-086e-43c7-9fb7-ec35a88a65a8" name="Changes" comment="update">
      <change beforePath="$PROJECT_DIR$/backend-admin/src/main/java/tech/wejoy/billiard/api/ClubApi.java" beforeDir="false" afterPath="$PROJECT_DIR$/backend-admin/src/main/java/tech/wejoy/billiard/api/ClubApi.java" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="master" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="jar://$MAVEN_REPOSITORY$/com/congeer/quarkus-core/1.0-SNAPSHOT/quarkus-core-1.0-20250422.152145-72.jar!/com/congeer/core/bean/Result.class" root0="SKIP_INSPECTION" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="mavenHomeTypeForPersistence" value="WRAPPER" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 5
}</component>
  <component name="ProjectId" id="2wnETo7p9IGqdGZyav85vwImxST" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Maven.backend-common [compile].executor&quot;: &quot;Run&quot;,
    &quot;Maven.backend-common [install].executor&quot;: &quot;Run&quot;,
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;Quarkus Dev Mode.admin.executor&quot;: &quot;Debug&quot;,
    &quot;Quarkus Dev Mode.api.executor&quot;: &quot;Debug&quot;,
    &quot;Quarkus.backend-admin.executor&quot;: &quot;Debug&quot;,
    &quot;Quarkus.backend-api.executor&quot;: &quot;Debug&quot;,
    &quot;Remote JVM Debug.admin (Remote).executor&quot;: &quot;Debug&quot;,
    &quot;Remote JVM Debug.api (Remote).executor&quot;: &quot;Debug&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;feat-meituan&quot;,
    &quot;ignore.virus.scanning.warn.message&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;C:/Users/<USER>/GitWork/partime/billiaard/backend&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RunManager" selected="Quarkus Dev Mode.admin">
    <configuration name="admin" type="QuarkusRunConfiguration" factoryName="QuarkusRunConfiguration">
      <module name="backend-admin" />
      <option name="profile" />
      <method v="2" />
    </configuration>
    <configuration name="api" type="QuarkusRunConfiguration" factoryName="QuarkusRunConfiguration">
      <module name="backend-api" />
      <option name="profile" />
      <method v="2" />
    </configuration>
    <configuration name="backend-admin" type="QuarkusRunConfigurationType" factoryName="Quarkus" nameIsGenerated="true">
      <module name="backend-admin" />
      <QsMavenRunConfiguration>
        <MavenSettings>
          <option name="myGeneralSettings" />
          <option name="myRunnerSettings" />
          <option name="myRunnerParameters">
            <MavenRunnerParameters>
              <option name="cmdOptions" />
              <option name="profiles">
                <set />
              </option>
              <option name="goals">
                <list>
                  <option value="quarkus:dev" />
                </list>
              </option>
              <option name="multimoduleDir" />
              <option name="pomFileName" value="pom.xml" />
              <option name="profilesMap">
                <map />
              </option>
              <option name="projectsCmdOptionValues">
                <list />
              </option>
              <option name="resolveToWorkspace" value="false" />
              <option name="workingDirPath" value="$PROJECT_DIR$/backend-admin" />
            </MavenRunnerParameters>
          </option>
        </MavenSettings>
        <targetMavenLocalRepo />
        <emulateTerminal>false</emulateTerminal>
        <profile>dev</profile>
      </QsMavenRunConfiguration>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="backend-api" type="QuarkusRunConfigurationType" factoryName="Quarkus" nameIsGenerated="true">
      <module name="backend-api" />
      <QsMavenRunConfiguration>
        <MavenSettings>
          <option name="myGeneralSettings" />
          <option name="myRunnerSettings" />
          <option name="myRunnerParameters">
            <MavenRunnerParameters>
              <option name="cmdOptions" />
              <option name="profiles">
                <set />
              </option>
              <option name="goals">
                <list>
                  <option value="quarkus:dev" />
                </list>
              </option>
              <option name="multimoduleDir" />
              <option name="pomFileName" value="pom.xml" />
              <option name="profilesMap">
                <map />
              </option>
              <option name="projectsCmdOptionValues">
                <list />
              </option>
              <option name="resolveToWorkspace" value="false" />
              <option name="workingDirPath" value="$PROJECT_DIR$/backend-api" />
            </MavenRunnerParameters>
          </option>
        </MavenSettings>
        <targetMavenLocalRepo />
        <emulateTerminal>false</emulateTerminal>
        <profile>dev</profile>
      </QsMavenRunConfiguration>
      <method v="2">
        <option name="Make" enabled="true" />
        <option name="Maven.BeforeRunTask" enabled="true" file="$PROJECT_DIR$/backend-common/pom.xml" goal="install" />
      </method>
    </configuration>
    <list>
      <item itemvalue="Quarkus Dev Mode.admin" />
      <item itemvalue="Quarkus Dev Mode.api" />
    </list>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="25db7718-086e-43c7-9fb7-ec35a88a65a8" name="Changes" comment="" />
      <created>1746668947721</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1746668947721</updated>
      <workItem from="1746668949036" duration="2586000" />
      <workItem from="1746671763481" duration="2359000" />
      <workItem from="1746683195838" duration="20515000" />
      <workItem from="1746771996084" duration="6011000" />
      <workItem from="1746888040586" duration="386000" />
      <workItem from="1746929077598" duration="2298000" />
      <workItem from="1747033341910" duration="619000" />
      <workItem from="1747039718149" duration="15000" />
    </task>
    <task id="LOCAL-00001" summary="update">
      <option name="closed" value="true" />
      <created>1746669045018</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1746669045018</updated>
    </task>
    <task id="LOCAL-00002" summary="update">
      <option name="closed" value="true" />
      <created>1746670723608</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1746670723608</updated>
    </task>
    <task id="LOCAL-00003" summary="update">
      <option name="closed" value="true" />
      <created>1746683244606</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1746683244607</updated>
    </task>
    <task id="LOCAL-00004" summary="update">
      <option name="closed" value="true" />
      <created>1746683800731</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1746683800731</updated>
    </task>
    <task id="LOCAL-00005" summary="update">
      <option name="closed" value="true" />
      <created>1746684208086</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1746684208086</updated>
    </task>
    <task id="LOCAL-00006" summary="update">
      <option name="closed" value="true" />
      <created>1746685071114</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1746685071114</updated>
    </task>
    <task id="LOCAL-00007" summary="update">
      <option name="closed" value="true" />
      <created>1746685111889</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1746685111889</updated>
    </task>
    <task id="LOCAL-00008" summary="update">
      <option name="closed" value="true" />
      <created>1746685890642</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1746685890642</updated>
    </task>
    <task id="LOCAL-00009" summary="update">
      <option name="closed" value="true" />
      <created>1746687401420</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1746687401420</updated>
    </task>
    <task id="LOCAL-00010" summary="update">
      <option name="closed" value="true" />
      <created>1746687650661</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1746687650661</updated>
    </task>
    <task id="LOCAL-00011" summary="update">
      <option name="closed" value="true" />
      <created>1746702207977</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1746702207977</updated>
    </task>
    <task id="LOCAL-00012" summary="update">
      <option name="closed" value="true" />
      <created>1746754569256</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1746754569256</updated>
    </task>
    <task id="LOCAL-00013" summary="update">
      <option name="closed" value="true" />
      <created>1746755822249</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1746755822249</updated>
    </task>
    <task id="LOCAL-00014" summary="update">
      <option name="closed" value="true" />
      <created>1746758303788</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1746758303788</updated>
    </task>
    <task id="LOCAL-00015" summary="update">
      <option name="closed" value="true" />
      <created>1746773168816</created>
      <option name="number" value="00015" />
      <option name="presentableId" value="LOCAL-00015" />
      <option name="project" value="LOCAL" />
      <updated>1746773168816</updated>
    </task>
    <task id="LOCAL-00016" summary="update">
      <option name="closed" value="true" />
      <created>1746888417890</created>
      <option name="number" value="00016" />
      <option name="presentableId" value="LOCAL-00016" />
      <option name="project" value="LOCAL" />
      <updated>1746888417890</updated>
    </task>
    <task id="LOCAL-00017" summary="update">
      <option name="closed" value="true" />
      <created>1748747570838</created>
      <option name="number" value="00017" />
      <option name="presentableId" value="LOCAL-00017" />
      <option name="project" value="LOCAL" />
      <updated>1748747570838</updated>
    </task>
    <task id="LOCAL-00018" summary="update">
      <option name="closed" value="true" />
      <created>1748768922091</created>
      <option name="number" value="00018" />
      <option name="presentableId" value="LOCAL-00018" />
      <option name="project" value="LOCAL" />
      <updated>1748768922091</updated>
    </task>
    <task id="LOCAL-00019" summary="update">
      <option name="closed" value="true" />
      <created>1748771035163</created>
      <option name="number" value="00019" />
      <option name="presentableId" value="LOCAL-00019" />
      <option name="project" value="LOCAL" />
      <updated>1748771035163</updated>
    </task>
    <task id="LOCAL-00020" summary="update">
      <option name="closed" value="true" />
      <created>1748784278920</created>
      <option name="number" value="00020" />
      <option name="presentableId" value="LOCAL-00020" />
      <option name="project" value="LOCAL" />
      <updated>1748784278920</updated>
    </task>
    <task id="LOCAL-00021" summary="update">
      <option name="closed" value="true" />
      <created>1748788022684</created>
      <option name="number" value="00021" />
      <option name="presentableId" value="LOCAL-00021" />
      <option name="project" value="LOCAL" />
      <updated>1748788022684</updated>
    </task>
    <task id="LOCAL-00022" summary="update">
      <option name="closed" value="true" />
      <created>1748836874535</created>
      <option name="number" value="00022" />
      <option name="presentableId" value="LOCAL-00022" />
      <option name="project" value="LOCAL" />
      <updated>1748836874535</updated>
    </task>
    <option name="localTasksCounter" value="23" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="origin/feat-delete" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <option name="CHECK_CODE_SMELLS_BEFORE_PROJECT_COMMIT" value="false" />
    <option name="CHECK_NEW_TODO" value="false" />
    <MESSAGE value="update" />
    <option name="LAST_COMMIT_MESSAGE" value="update" />
    <option name="NON_MODAL_COMMIT_POSTPONE_SLOW_CHECKS" value="false" />
  </component>
</project>