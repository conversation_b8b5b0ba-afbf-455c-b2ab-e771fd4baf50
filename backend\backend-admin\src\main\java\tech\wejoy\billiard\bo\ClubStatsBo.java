package tech.wejoy.billiard.bo;

import lombok.Data;
import org.eclipse.microprofile.openapi.annotations.media.Schema;

import java.math.BigDecimal;

@Data
public class ClubStatsBo {

    @Schema(description = "日活用户数")
    private Integer userCount;

    @Schema(description = "总订单数")
    private Integer orderCount;

    @Schema(description = "三方订单数")
    private Integer thirdOrderCount;

    @Schema(description = "会员充值数")
    private Integer memberRechargeCount;

    @Schema(description = "非会员订单数")
    private Integer nonMemberOrderCount;

    @Schema(description = "会员转化比例")
    private BigDecimal memberConvertRate;

    @Schema(description = "会员充值比例")
    private BigDecimal memberRechargeRate;

    @Schema(description = "平均小时单价")
    private BigDecimal perPrice;

    @Schema(description = "总小时数")
    private BigDecimal totalHours;

    @Schema(description = "平均小时数")
    private BigDecimal perHours;


}
