package tech.wejoy.billiard.api;

import com.congeer.core.bean.Page;
import com.congeer.security.quarkus.annotation.Authorized;
import com.congeer.security.quarkus.annotation.Permission;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import lombok.RequiredArgsConstructor;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;
import tech.wejoy.billiard.bo.AdminClubBo;
import tech.wejoy.billiard.dto.*;
import tech.wejoy.billiard.service.AdminClubService;

import java.io.File;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;

@Path("/club")
@Tag(name = "门店")
@Produces(MediaType.APPLICATION_JSON)
@Authorized({"default", "business"})
@RequiredArgsConstructor
public class ClubApi {

    private final AdminClubService clubService;

    @GET
    @Path("/list")
    @Operation(summary = "获取门店列表")
    public Page<AdminClubBo> page(AdminClubQueryDto dto) {
        return clubService.listAdmin(dto);
    }

    @GET
    @Path("/{id}")
    @Operation(summary = "获取门店详情")
    public AdminClubBo detail(@PathParam("id") Long id) {
        return clubService.detail(id);
    }

    @POST
    @Path("/")
    @Operation(summary = "创建门店")
    public AdminClubBo create(AdminClubDto bo) {
        return clubService.save(bo);
    }

    @GET
    @Path("/options")
    @Operation(summary = "获取门店选项")
    public List<AdminClubBo> options(@QueryParam("tenantId") Long tenantId) {
        return clubService.optionsClub(tenantId);
    }

    @PUT
    @Path("/{id}")
    @Operation(summary = "更新门店")
    @Permission({"business_club:write"})
    public AdminClubBo update(@PathParam("id") Long id, AdminClubDto bo) {
        bo.setId(id);
        return clubService.save(bo);
    }

    @POST
    @Path("/distribution")
    @Operation(summary = "分配门店")
    public void distribution(DistributionDto dto) {
        clubService.distribution(dto);
    }

    @POST
    @Path("/{id}/table/gen")
    @Operation(summary = "生成门店桌位")
    public void genTable(@PathParam("id") Long id, GenTableDto dto) {
        clubService.genTable(id, dto.getTableCount());
    }

    @GET
    @Path("/{id}/code")
    @Operation(summary = "获取桌台码")
    @Produces(MediaType.APPLICATION_OCTET_STREAM)
    public Response getBindQrCode(@PathParam("id") Long id) {
        File entity = clubService.getClubTablesCode(id);
        return Response.ok(entity)
                .header("Content-Disposition", "attachment; filename=" + URLEncoder.encode(entity.getName(), StandardCharsets.UTF_8))
                .build();
    }

    @POST
    @Path("/{id}/table/config")
    @Operation(summary = "一键配置桌位")
    public void configTable(@PathParam("id") Long id, ConfigTableDto dto) {
        clubService.configTable(id, dto.getTimeSlots(), dto.getStatus());
    }

    @POST
    @Path("/{id}/close")
    @Operation(summary = "关闭门店")
    @Authorized("default")
    public void closeStore(@PathParam("id") Long id) {
        clubService.closeStore(id);
    }

    @POST
    @Path("/{id}/reopen")
    @Operation(summary = "重新打开门店")
    @Authorized("default")
    public void reopenStore(@PathParam("id") Long id) {
        clubService.reopenStore(id);
    }

    @POST
    @Path("/{id}/out-of-business")
    @Operation(summary = "停业门店")
    @Authorized("default")
    public void outOfBusiness(@PathParam("id") Long id) {
        clubService.outOfBusiness(id);
    }

}
