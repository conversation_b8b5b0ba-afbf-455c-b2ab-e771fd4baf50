package tech.wejoy.billiard.dto;

import com.congeer.web.bean.request.PageRequest;
import jakarta.ws.rs.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.eclipse.microprofile.openapi.annotations.media.Schema;

@EqualsAndHashCode(callSuper = true)
@Data
public class TenantQueryDto extends PageRequest {

    @QueryParam("name")
    @Schema(description = "名称")
    private String name;

}
