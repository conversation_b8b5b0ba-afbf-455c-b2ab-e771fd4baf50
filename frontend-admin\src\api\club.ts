import { http } from ".";

const base = "/api/club";

type ClubQueryDto = {
  size: number;
  current: number;
  name: string;
  city: string;
  status: string;
  tenantId: number;
};

export const clubList = async (params: Partial<ClubQueryDto>) => {
  return await http.get(base + "/list", { params }).then((res) => res.data);
};

export const createClub = async (data: any) => {
  return await http.post(base + "/", data).then((res) => res.data);
};

export const clubDetail = async (id: number) => {
  return await http.get(base + "/" + id).then((res) => res.data);
};

export const clubOptions = async (tenantId?: number) => {
  return await http
    .get(base + "/options", { params: { tenantId } })
    .then((res) => res.data);
};

export const clubTablesCode = async (id: number) => {
  return await http.get(base + `/${id}/code/`, { responseType: "blob" });
};

export const clubGenTable = async (id: number, tableCount: number) => {
  return await http
    .post(base + `/${id}/table/gen`, { tableCount })
    .then((res) => res.data);
};

export const clubChangeRate = async (id: number, rate: number) => {
  return await http.put(base + `/${id}/rate/${rate}`).then((res) => res.data);
};

// 门店操作接口
export const clubStop = async (id: number) => {
  return await http.post(base + `/${id}/stop`).then((res) => res.data);
};

export const clubOutOfBusiness = async (id: number) => {
  return await http.post(base + `/${id}/out-of-business`).then((res) => res.data);
};

export const clubClose = async (id: number) => {
  return await http.post(base + `/${id}/close`).then((res) => res.data);
};

export const clubReopen = async (id: number) => {
  return await http.post(base + `/${id}/reopen`).then((res) => res.data);
};
