package tech.wejoy.billiard.bo;

import lombok.Data;
import org.eclipse.microprofile.openapi.annotations.media.Schema;

import java.math.BigDecimal;

@Data
public class RevenueBo {

    @Schema(description = "日期")
    private String date;

    @Schema(description = "总收入")
    private BigDecimal total = BigDecimal.ZERO;

    @Schema(description = "现金")
    private BigDecimal cash = BigDecimal.ZERO;

    @Schema(description = "单店卡充值")
    private BigDecimal clubRecharge = BigDecimal.ZERO;

    @Schema(description = "单店卡使用")
    private BigDecimal clubUsed = BigDecimal.ZERO;

    @Schema(description = "团购券")
    private BigDecimal ticket = BigDecimal.ZERO;

    @Schema(description = "订单数")
    private Integer orderCount = 0;

}
